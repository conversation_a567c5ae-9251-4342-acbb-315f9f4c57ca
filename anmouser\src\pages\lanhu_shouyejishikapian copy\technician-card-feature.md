# 地图技师卡片功能说明

## 功能概述
当用户在地图视图中点击技师标记时，会在底部导航上方显示该技师的详细卡片信息。卡片样式复用了技师列表的一行一列布局。

## 🔧 问题修复说明

### 样式修复
修复了卡片样式错乱的问题：
- **问题**：原始技师列表样式使用了固定宽度和绝对定位，在固定定位的卡片容器中会导致布局错乱
- **解决方案**：为选中技师卡片重写了完整的样式，使用flex布局替代固定定位，确保在不同容器中都能正常显示
- **改进**：优化了响应式布局，使卡片在不同屏幕尺寸下都能正确显示

### 事件冲突修复
修复了点击技师标记后卡片立即关闭的问题：
- **问题**：点击技师标记时，事件冒泡到地图容器，导致卡片刚显示就被地图点击事件关闭
- **解决方案**：添加了 `isMarkerClicked` 标记，在点击标记后100ms内忽略地图点击事件
- **效果**：现在点击技师标记能正常显示卡片，点击地图空白区域才会关闭卡片

### 地图切换显示修复
修复了从列表切换到地图时地图不显示的问题：
- **根本原因**：使用`v-show`隐藏地图容器时，Leaflet无法在隐藏状态下正确计算尺寸
- **问题分析**：
  - 地图只在页面加载时初始化一次，切换时不会重新渲染
  - CSS样式冲突，存在重复的`.map-container`样式定义
  - 地图容器在隐藏状态下高度为0，导致Leaflet计算错误
  - DOM状态切换需要时间，立即刷新可能失败
- **解决方案**：
  - 使用 `v-show` 替代 `v-if`，保持DOM元素始终存在
  - 在 `switchTab` 方法中添加延迟，等待DOM显示状态切换完成
  - 简化切换逻辑：地图实例存在时只调用 `invalidateSize()`，不重新创建
  - 移除了重复的CSS样式定义，统一使用新的地图容器样式
  - 优化性能：避免不必要的地图重新初始化
- **效果**：现在从列表切换到地图时，地图能够快速正常显示，性能更佳

### 地图切换动画
为地图切换添加了平滑的动画效果，提升用户体验：
- **动画类型**：从下方滑入 + 缩放 + 淡入效果
- **动画时长**：500ms 主动画 + 300ms 内容延迟淡入
- **缓动函数**：cubic-bezier(0.4, 0, 0.2, 1) 提供自然的动画曲线
- **触发机制**：每次切换到地图时自动触发动画
- **性能优化**：使用 CSS 动画而非 JavaScript，确保流畅性

## 实现的功能

### ✅ 技师卡片显示
- 点击地图上的技师标记后，在底部导航上方显示技师卡片
- 卡片包含技师的完整信息：头像、姓名、评分、服务次数、距离、状态等
- 卡片样式与技师列表保持一致

### ✅ 交互功能
- **显示条件**：只在地图视图（tabsIndex === 0）且有选中技师时显示
- **关闭功能**：点击地图空白区域自动关闭卡片
- **动画效果**：卡片显示/隐藏时有平滑的滑动动画

### ✅ 兼容性支持
- **H5环境**：支持Leaflet地图标记点击
- **小程序环境**：支持uni-app原生地图标记点击
- **响应式设计**：卡片在不同屏幕尺寸下都能正常显示

## 使用方法

### 测试步骤
1. 切换到地图视图（点击"地图"tab）
2. 等待地图加载完成，显示技师标记点
3. 点击任意技师标记
4. 观察底部导航上方出现的技师卡片
5. 点击地图空白区域关闭卡片

### 预期效果
- **点击标记**：卡片从底部滑入显示
- **显示内容**：与技师列表中的卡片样式完全一致
- **关闭卡片**：点击地图空白区域，卡片向底部滑出隐藏
- **切换视图**：切换到列表视图时卡片自动隐藏

## 技术实现

### 数据结构
```javascript
// 选中的技师数据
selectedTechnician: null // 存储当前选中的技师对象
```

### 关键方法
```javascript
// H5地图标记点击
onTechnicianClick(technician) {
  this.selectedTechnician = technician;
}

// uni-app地图标记点击
onMarkerTap(e) {
  const technician = this.technicianList.find(item => item.id === markerId);
  this.selectedTechnician = technician;
}

// 关闭卡片
closeSelectedTechnician() {
  this.selectedTechnician = null;
}

// 地图点击事件
onMapClick() {
  // 防止标记点击事件冲突
  if (this.isMarkerClicked) {
    return;
  }
  if (this.selectedTechnician) {
    this.closeSelectedTechnician();
  }
}

// 技师标记点击事件
onTechnicianClick(technician) {
  this.selectedTechnician = technician;
  // 设置标记，防止地图点击事件立即关闭卡片
  this.isMarkerClicked = true;
  setTimeout(() => {
    this.isMarkerClicked = false;
  }, 100);
}

// 带动画的切换逻辑
switchTab(index) {
  if (index === 0) {
    this.tabsIndex = 0;
    this.mapAnimating = true; // 触发动画

    this.$nextTick(() => {
      setTimeout(() => {
        if (this.isH5 && this.h5Map) {
          // 地图实例存在，只需重新计算尺寸
          this.h5Map.invalidateSize();
        } else if (this.isH5 && !this.h5Map) {
          // 地图实例不存在，初始化
          this.initH5Map();
        }

        // 动画完成后重置状态
        setTimeout(() => {
          this.mapAnimating = false;
        }, 500);
      }, 100);
    });
  }
}
```

### 样式特点
- **固定定位**：`position: fixed; bottom: 120rpx`
- **毛玻璃效果**：`backdrop-filter: blur(10rpx)`
- **阴影效果**：`box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.15)`
- **圆角设计**：`border-radius: 20rpx`

## 注意事项
1. 卡片只在地图视图中显示，切换到列表视图会自动隐藏
2. 卡片使用固定定位，不会影响页面滚动
3. 点击地图空白区域可以关闭卡片，操作更加自然
4. 卡片内容完全复用技师列表的样式，保持界面一致性
5. 支持H5和小程序双平台的地图点击事件
