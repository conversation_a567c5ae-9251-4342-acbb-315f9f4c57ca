# 定位功能测试说明

## 功能概述
点击定位图标可以获取用户当前位置，并更新地址显示。支持H5和微信小程序环境。

## 测试步骤

### H5环境测试
1. 在浏览器中打开页面（需要HTTPS环境）
2. 点击定位图标（thumbnail_4）
3. 浏览器会弹出定位权限请求，点击"允许"
4. 等待定位完成，地址文本会更新
5. 地图中心点会移动到当前位置

### 微信小程序测试
1. 在微信开发者工具中打开项目
2. 点击定位图标
3. 小程序会请求定位权限，点击"允许"
4. 等待定位完成，地址文本会更新
5. 地图中心点会移动到当前位置

## 预期结果
- 定位成功：地址文本从"重庆市渝中区名族路188号..."更新为实际定位地址
- 定位失败：显示错误提示，地址保持原样
- 定位过程中：显示"正在定位..."加载提示

## 错误处理
- 用户拒绝定位权限：提示"用户拒绝了定位权限"
- 定位超时：提示"定位超时，请重试"
- 浏览器不支持定位：提示"浏览器不支持定位功能"
- 位置信息不可用：提示"位置信息不可用"

## 技术实现
- H5环境：使用 `navigator.geolocation.getCurrentPosition`
- 小程序环境：使用 `uni.getLocation`
- 坐标系：使用GCJ02（国测局坐标系）
- 地址解析：当前使用模拟地址，实际项目中可接入腾讯地图或高德地图API

## 注意事项
1. H5环境需要HTTPS才能正常使用定位功能
2. 微信小程序需要在manifest.json中配置定位权限
3. 首次使用需要用户授权定位权限
4. 定位过程中防止重复点击
