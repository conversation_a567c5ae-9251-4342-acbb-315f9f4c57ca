// 动画定义
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.page-scroll {
  width: 100%;
  height: 100vh;
  background-color: rgba(248, 248, 248, 1);
}

.page {
  background-color: rgba(248, 248, 248, 1);
  position: relative;
  width: 750rpx;
  min-height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;
  .section_1 {
    background-color: rgba(0, 0, 0, 1);
    width: 750rpx;
    height: 172rpx;
    .box_1 {
      background-color: rgba(255, 255, 255, 1);
      width: 750rpx;
      height: 64rpx;
      .text_1 {
        width: 64rpx;
        height: 36rpx;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 24rpx;
        font-family: Inter-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 24rpx;
        margin: 14rpx 0 0 32rpx;
      }
      .thumbnail_1 {
        width: 36rpx;
        height: 36rpx;
        margin: 14rpx 0 0 502rpx;
      }
      .thumbnail_2 {
        width: 36rpx;
        height: 36rpx;
        margin: 14rpx 0 0 6rpx;
      }
      .thumbnail_3 {
        width: 38rpx;
        height: 38rpx;
        margin: 14rpx 30rpx 0 6rpx;
      }
    }
    .box_2 {
      background-color: rgba(255, 255, 255, 1);
      width: 750rpx;
      height: 108rpx;
      .text_2 {
        width: 128rpx;
        height: 44rpx;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 32rpx;
        font-family: Inter-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 44rpx;
        margin: 32rpx 0 0 24rpx;
      }
      .image_1 {
        width: 174rpx;
        height: 64rpx;
        margin: 22rpx 24rpx 0 0;
      }
    }
  }
  .section_2 {
    position: relative;
    width: 750rpx;
    height: 1454rpx;
    margin-bottom: 2rpx;
    .group_1 {
      background-color: rgba(11, 206, 148, 1);
      width: 750rpx;
      height: 324rpx;
      .box_3 {
        width: 700rpx;
        height: 46rpx;
        margin: 30rpx 0 0 26rpx;
        .thumbnail_4 {
          width: 32rpx;
          height: 34rpx;
          margin-top: 2rpx;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            opacity: 0.8;
            transform: scale(1.1);
          }

          &:active {
            opacity: 0.6;
            transform: scale(0.95);
          }
        }
        .image-text_1 {
          width: 422rpx;
          height: 30rpx;
          margin: 4rpx 0 0 22rpx;
          .text-group_1 {
            width: 372rpx;
            height: 30rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 30rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 30rpx;
          }
          .box_4 {
            width: 32rpx;
            margin-top: 4rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            .vector-icon {
              width: 16rpx;
              height: 12rpx;
              filter: brightness(0) invert(1);
            }
          }
        }
        .block_1 {
          background-color: rgba(255, 255, 255, 0.2);
          border-radius: 2px 0px 2px 0px;
          width: 92rpx;
          height: 46rpx;
          margin-left: 40rpx;
          cursor: pointer;
          transition: all 0.3s ease;
          box-sizing: border-box;
          border: 1px solid transparent;
          display: flex;
          align-items: center;
          justify-content: center;
          .image-text_2 {
            width: 64rpx;
            height: 18rpx;
            margin: 0;
            display: flex;
            align-items: center;
            .thumbnail_5 {
              width: 18rpx;
              height: 18rpx;
              transition: filter 0.3s ease;
            }
            .text-group_2 {
              width: 40rpx;
              height: 18rpx;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 20rpx;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 20rpx;
              transition: color 0.3s ease;
            }
          }
        }
        .block_2 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 0px 2px 0px 2px;
          width: 92rpx;
          height: 46rpx;
          border: 1px solid rgba(224, 224, 224, 1);
          cursor: pointer;
          transition: all 0.3s ease;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          justify-content: center;
          .image-text_3 {
            width: 62rpx;
            height: 18rpx;
            margin: 0;
            display: flex;
            align-items: center;
            .thumbnail_6 {
              width: 18rpx;
              height: 18rpx;
              transition: filter 0.3s ease;
            }
            .text-group_3 {
              width: 40rpx;
              height: 18rpx;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 20rpx;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 20rpx;
              transition: color 0.3s ease;
            }
          }
        }

        // Tab选中状态样式
        .block_1.tab-active {
          background-color: rgba(255, 255, 255, 1) !important;
          border: 1px solid rgba(224, 224, 224, 1) !important;

          .image-text_2 {
            .thumbnail_5 {
              filter: brightness(0) saturate(100%) invert(47%) sepia(96%) saturate(1352%) hue-rotate(134deg) brightness(98%) contrast(96%);
            }
            .text-group_2 {
              color: #0bce94 !important;
            }
          }
        }

        .block_2.tab-active {
          background-color: rgba(255, 255, 255, 1) !important;
          border: 1px solid rgba(224, 224, 224, 1) !important;

          .image-text_3 {
            .thumbnail_6 {
              filter: brightness(0) saturate(100%) invert(47%) sepia(96%) saturate(1352%) hue-rotate(134deg) brightness(98%) contrast(96%);
            }
            .text-group_3 {
              color: #0bce94 !important;
            }
          }
        }

        // 未选中状态样式
        .block_1:not(.tab-active) {
          background-color: #3CD8A9 !important;
          border: 1px solid transparent !important;

          .image-text_2 {
            .thumbnail_5 {
              filter: brightness(0) invert(1);
            }
            .text-group_2 {
              color: rgba(255, 255, 255, 1) !important;
            }
          }
        }

        .block_2:not(.tab-active) {
          background-color: #3CD8A9 !important;
          border: 1px solid transparent !important;

          .image-text_3 {
            .thumbnail_6 {
              filter: brightness(0) invert(1);
            }
            .text-group_3 {
              color: rgba(255, 255, 255, 1) !important;
            }
          }
        }
      }
      .box_5 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 50px;
        width: 702rpx;
        height: 76rpx;
        margin: 22rpx 0 0 24rpx;
        .image-text_4 {
          width: 140rpx;
          height: 26rpx;
          margin: 26rpx 0 0 34rpx;
          .text-group_4 {
            width: 104rpx;
            height: 26rpx;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 26rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26rpx;
          }
          .group_2 {
            width: 20rpx;
            margin-top: 6rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            .vector-icon {
              width: 16rpx;
              height: 12rpx;
              filter: brightness(0);
            }
          }
        }
        .text_3 {
          width: 234rpx;
          height: 26rpx;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 26rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26rpx;
          margin: 26rpx 0 0 54rpx;
        }
        .label_1 {
          width: 48rpx;
          height: 48rpx;
          margin: 14rpx 22rpx 0 170rpx;
        }
      }
      .box_6 {
        width: 100%;
        height: 46rpx;
        margin: 28rpx 0 0 0;
        padding: 0 30rpx;
        box-sizing: border-box;
        overflow-x: auto;
        overflow-y: hidden;

        // 完全隐藏滚动条的终极方案
        &::-webkit-scrollbar {
          display: none !important;
          width: 0 !important;
          height: 0 !important;
          background: transparent !important;
        }

        &::-webkit-scrollbar-track {
          display: none !important;
          background: transparent !important;
        }

        &::-webkit-scrollbar-thumb {
          display: none !important;
          background: transparent !important;
        }

        &::-webkit-scrollbar-corner {
          display: none !important;
          background: transparent !important;
        }

        // 兼容其他浏览器
        scrollbar-width: none !important;
        -ms-overflow-style: none !important;
        overflow: -moz-scrollbars-none;

        .scroll-content {
          display: flex;
          flex-direction: row;
          align-items: center;
          white-space: nowrap;
          min-width: max-content;
        }

        .service-item {
          flex-shrink: 0;
          margin-right: 16rpx;
          background-color: rgba(34, 34, 34, 0.1);
          border-radius: 100px;
          height: 46rpx;
          width: 140rpx;
          display: flex;
          align-items: center;
          justify-content: center;

          &:first-child {
            margin-left: 0;
            background-color: rgba(255, 255, 255, 1);

            .service-text {
              color: rgba(11, 206, 148, 1);
            }
          }

          &:last-child {
            margin-right: 0;
          }

          .service-text {
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 26rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26rpx;
          }
        }
        .text-wrapper_1 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 100px;
          height: 46rpx;
          width: 140rpx;
          .text_4 {
            width: 104rpx;
            height: 38rpx;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 26rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26rpx;
            margin: 4rpx 0 0 18rpx;
          }
        }
        .text-wrapper_2 {
          background-color: rgba(34, 34, 34, 0.1);
          border-radius: 100px;
          height: 46rpx;
          width: 140rpx;
          .text_5 {
            width: 104rpx;
            height: 38rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 26rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26rpx;
            margin: 4rpx 0 0 18rpx;
          }
        }
        .text-wrapper_3 {
          background-color: rgba(34, 34, 34, 0.1);
          border-radius: 100px;
          height: 46rpx;
          width: 140rpx;
          .text_6 {
            width: 104rpx;
            height: 38rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 26rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26rpx;
            margin: 4rpx 0 0 18rpx;
          }
        }
        .text-wrapper_4 {
          background-color: rgba(34, 34, 34, 0.1);
          border-radius: 100px;
          height: 46rpx;
          width: 140rpx;
          .text_7 {
            width: 104rpx;
            height: 38rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 26rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26rpx;
            margin: 4rpx 0 0 18rpx;
          }
        }
      }

      .box_7 {
        width: 700rpx;
        height: 36rpx;
        margin: 22rpx 0 18rpx 34rpx;
        display: flex;
        align-items: center;
        .text_8 {
          width: 96rpx;
          height: 36rpx;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 24rpx;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
        }
        .group_3 {
          width: 18rpx;
          margin-left: 22rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          .vector-icon {
            width: 14rpx;
            height: 10rpx;
            filter: brightness(0) invert(1);
          }
        }
        .text_9 {
          width: 96rpx;
          height: 36rpx;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 24rpx;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
          margin-left: 52rpx;
        }
        .group_4 {
          width: 18rpx;
          margin-left: 22rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          .vector-icon {
            width: 14rpx;
            height: 10rpx;
            filter: brightness(0) invert(1);
          }
        }
        .text_10 {
          width: 96rpx;
          height: 36rpx;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 24rpx;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
          margin-left: 52rpx;
        }
        .group_5 {
          width: 18rpx;
          margin-left: 22rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          .vector-icon {
            width: 14rpx;
            height: 10rpx;
            filter: brightness(0) invert(1);
          }
        }
        .text_11 {
          width: 96rpx;
          height: 36rpx;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 24rpx;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
          margin-left: 52rpx;
        }
        .group_6 {
          width: 18rpx;
          margin-left: 22rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          .vector-icon {
            width: 14rpx;
            height: 10rpx;
            filter: brightness(0) invert(1);
          }
        }
      }
    }
    .group_7 {
      width: 702rpx;
      height: 336rpx;
      margin: 20rpx 0 0 24rpx;
      .group_8 {
        height: 336rpx;
        background: url(/static/lanhu_shouyejishikapian/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png)
          100% no-repeat;
        background-size: 100% 100%;
        width: 340rpx;
        .group_9 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 20px;
          width: 222rpx;
          height: 42rpx;
          margin: 272rpx 0 0 50rpx;
          .text-wrapper_5 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 20px;
            height: 42rpx;
            width: 122rpx;
            .text_12 {
              width: 98rpx;
              height: 24rpx;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 24rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 24rpx;
              margin: 8rpx 0 0 8rpx;
            }
          }
          .text_13 {
            width: 62rpx;
            height: 24rpx;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 24rpx;
            margin: 8rpx 26rpx 0 12rpx;
          }
        }
      }
      .group_10 {
        height: 336rpx;
        background: url(/static/lanhu_shouyejishikapian/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png)
          100% no-repeat;
        background-size: 100% 100%;
        width: 340rpx;
        .box_8 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 20px;
          width: 222rpx;
          height: 42rpx;
          margin: 272rpx 0 0 50rpx;
          .text-wrapper_6 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 20px;
            height: 42rpx;
            width: 122rpx;
            .text_14 {
              width: 98rpx;
              height: 24rpx;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 24rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 24rpx;
              margin: 8rpx 0 0 8rpx;
            }
          }
          .text_15 {
            width: 62rpx;
            height: 24rpx;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 24rpx;
            margin: 8rpx 26rpx 0 12rpx;
          }
        }
      }
    }
    .group_11 {
      width: 702rpx;
      height: 286rpx;
      margin-left: 24rpx;
      .box_9 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 0px 0px 5px 5px;
        width: 340rpx;
        height: 286rpx;
        .box_10 {
          width: 202rpx;
          height: 34rpx;
          margin: 20rpx 0 0 22rpx;
          .text_16 {
            width: 92rpx;
            height: 30rpx;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 30rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 30rpx;
          }
          .text-wrapper_7 {
            background-color: rgba(30, 30, 30, 1);
            border-radius: 50px;
            height: 32rpx;
            margin-top: 2rpx;
            width: 94rpx;
            .text_17 {
              width: 66rpx;
              height: 16rpx;
              overflow-wrap: break-word;
              color: rgba(234, 225, 196, 1);
              font-size: 20rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 20rpx;
              margin: 6rpx 0 0 14rpx;
            }
          }
        }
        .image-text_5 {
          width: 194rpx;
          height: 24rpx;
          margin: 16rpx 0 0 22rpx;
          .group_12 {
            width: 24rpx;
            height: 22rpx;
            background: url(/static/lanhu_shouyejishikapian/FigmaDDSSlicePNG751b001b58a95440d7eae2a8c0959d9d.png)
              100% no-repeat;
            background-size: 100% 100%;
            margin-top: 2rpx;
          }
          .text-group_5 {
            width: 164rpx;
            height: 24rpx;
            .text_18 {
              width: 16rpx;
              height: 24rpx;
              overflow-wrap: break-word;
              color: rgba(247, 160, 68, 1);
              font-size: 24rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24rpx;
            }
            .text_19 {
              width: 138rpx;
              height: 24rpx;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 24rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24rpx;
            }
          }
        }
        .image-text_6 {
          width: 146rpx;
          height: 24rpx;
          margin: 16rpx 0 0 24rpx;
          .thumbnail_7 {
            width: 18rpx;
            height: 18rpx;
            margin-top: 4rpx;
          }
          .text-group_6 {
            width: 122rpx;
            height: 24rpx;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
          }
        }
        .box_11 {
          width: 292rpx;
          height: 54rpx;
          margin: 22rpx 0 0 26rpx;
          .text-wrapper_8 {
            border-radius: 4px;
            height: 54rpx;
            border: 1px solid rgba(11, 206, 148, 1);
            width: 134rpx;
            .text_20 {
              width: 102rpx;
              height: 30rpx;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 24rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24rpx;
              margin: 14rpx 0 0 16rpx;
            }
          }
          .text-wrapper_9 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 5px;
            height: 54rpx;
            width: 134rpx;
            .text_21 {
              width: 98rpx;
              height: 24rpx;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 24rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24rpx;
              margin: 14rpx 0 0 22rpx;
            }
          }
        }
        .box_12 {
          width: 292rpx;
          height: 28rpx;
          margin: 34rpx 0 14rpx 26rpx;
          .group_13 {
            background-color: rgba(150, 150, 150, 1);
            width: 32rpx;
            height: 28rpx;
          }
          .text_22 {
            width: 16rpx;
            height: 24rpx;
            overflow-wrap: break-word;
            color: rgba(150, 150, 150, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
            margin: 2rpx 0 0 6rpx;
          }
          .thumbnail_8 {
            width: 30rpx;
            height: 28rpx;
            margin-left: 50rpx;
          }
          .text_23 {
            width: 16rpx;
            height: 24rpx;
            overflow-wrap: break-word;
            color: rgba(150, 150, 150, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
            margin: 2rpx 0 0 6rpx;
          }
          .image-text_7 {
            width: 84rpx;
            height: 24rpx;
            margin: 2rpx 0 0 52rpx;
            .group_14 {
              background-color: rgba(150, 150, 150, 1);
              width: 26rpx;
              height: 24rpx;
            }
            .text-group_7 {
              width: 50rpx;
              height: 24rpx;
              overflow-wrap: break-word;
              color: rgba(150, 150, 150, 1);
              font-size: 24rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24rpx;
            }
          }
        }
      }
      .box_13 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 0px 0px 5px 5px;
        width: 340rpx;
        height: 286rpx;
        .group_15 {
          width: 202rpx;
          height: 34rpx;
          margin: 20rpx 0 0 22rpx;
          .text_24 {
            width: 92rpx;
            height: 30rpx;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 30rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 30rpx;
          }
          .text-wrapper_10 {
            background-color: rgba(30, 30, 30, 1);
            border-radius: 50px;
            height: 32rpx;
            margin-top: 2rpx;
            width: 94rpx;
            .text_25 {
              width: 66rpx;
              height: 16rpx;
              overflow-wrap: break-word;
              color: rgba(234, 225, 196, 1);
              font-size: 20rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 20rpx;
              margin: 6rpx 0 0 14rpx;
            }
          }
        }
        .image-text_8 {
          width: 194rpx;
          height: 24rpx;
          margin: 16rpx 0 0 22rpx;
          .section_3 {
            width: 24rpx;
            height: 22rpx;
            background: url(/static/lanhu_shouyejishikapian/FigmaDDSSlicePNG751b001b58a95440d7eae2a8c0959d9d.png)
              100% no-repeat;
            background-size: 100% 100%;
            margin-top: 2rpx;
          }
          .text-group_8 {
            width: 164rpx;
            height: 24rpx;
            .text_26 {
              width: 16rpx;
              height: 24rpx;
              overflow-wrap: break-word;
              color: rgba(247, 160, 68, 1);
              font-size: 24rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24rpx;
            }
            .text_27 {
              width: 138rpx;
              height: 24rpx;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 24rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24rpx;
            }
          }
        }
        .image-text_9 {
          width: 146rpx;
          height: 24rpx;
          margin: 16rpx 0 0 24rpx;
          .thumbnail_9 {
            width: 18rpx;
            height: 18rpx;
            margin-top: 4rpx;
          }
          .text-group_9 {
            width: 122rpx;
            height: 24rpx;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
          }
        }
        .group_16 {
          width: 292rpx;
          height: 54rpx;
          margin: 22rpx 0 0 26rpx;
          .text-wrapper_11 {
            border-radius: 4px;
            height: 54rpx;
            border: 1px solid rgba(11, 206, 148, 1);
            width: 134rpx;
            .text_28 {
              width: 102rpx;
              height: 30rpx;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 24rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24rpx;
              margin: 14rpx 0 0 16rpx;
            }
          }
          .text-wrapper_12 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 5px;
            height: 54rpx;
            width: 134rpx;
            .text_29 {
              width: 98rpx;
              height: 24rpx;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 24rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24rpx;
              margin: 14rpx 0 0 22rpx;
            }
          }
        }
        .group_17 {
          width: 292rpx;
          height: 28rpx;
          margin: 34rpx 0 14rpx 26rpx;
          .box_14 {
            background-color: rgba(150, 150, 150, 1);
            width: 32rpx;
            height: 28rpx;
          }
          .text_30 {
            width: 16rpx;
            height: 24rpx;
            overflow-wrap: break-word;
            color: rgba(150, 150, 150, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
            margin: 2rpx 0 0 6rpx;
          }
          .box_15 {
            background-color: rgba(150, 150, 150, 1);
            width: 30rpx;
            height: 28rpx;
            margin-left: 50rpx;
          }
          .text_31 {
            width: 16rpx;
            height: 24rpx;
            overflow-wrap: break-word;
            color: rgba(150, 150, 150, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
            margin: 2rpx 0 0 6rpx;
          }
          .image-text_10 {
            width: 84rpx;
            height: 24rpx;
            margin: 2rpx 0 0 52rpx;
            .section_4 {
              background-color: rgba(150, 150, 150, 1);
              width: 26rpx;
              height: 24rpx;
            }
            .text-group_10 {
              width: 50rpx;
              height: 24rpx;
              overflow-wrap: break-word;
              color: rgba(150, 150, 150, 1);
              font-size: 24rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24rpx;
            }
          }
        }
      }
    }
    .group_18 {
      width: 702rpx;
      height: 336rpx;
      margin: 20rpx 0 0 24rpx;
      .group_19 {
        width: 340rpx;
        height: 336rpx;
        background: url(/static/lanhu_shouyejishikapian/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png)
          100% no-repeat;
        background-size: 100% 100%;
        .text-wrapper_13 {
          background-color: rgba(0, 0, 0, 0.5);
          border-radius: 5px;
          height: 68rpx;
          width: 188rpx;
          margin: 68rpx 0 0 112rpx;
          .text_32 {
            width: 112rpx;
            height: 28rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 28rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 28rpx;
            margin: 20rpx 0 0 38rpx;
          }
        }
        .block_3 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 20px;
          width: 222rpx;
          height: 42rpx;
          margin: 136rpx 0 22rpx 50rpx;
          .text-wrapper_14 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 20px;
            height: 42rpx;
            width: 122rpx;
            .text_33 {
              width: 98rpx;
              height: 24rpx;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 24rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 24rpx;
              margin: 8rpx 0 0 8rpx;
            }
          }
          .text_34 {
            width: 62rpx;
            height: 24rpx;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 24rpx;
            margin: 8rpx 26rpx 0 12rpx;
          }
        }
      }
      .group_20 {
        height: 336rpx;
        background: url(/static/lanhu_shouyejishikapian/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png)
          100% no-repeat;
        background-size: 100% 100%;
        width: 340rpx;
        .box_16 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 20px;
          width: 222rpx;
          height: 42rpx;
          margin: 272rpx 0 0 50rpx;
          .text-wrapper_15 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 20px;
            height: 42rpx;
            width: 122rpx;
            .text_35 {
              width: 98rpx;
              height: 24rpx;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 24rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 24rpx;
              margin: 8rpx 0 0 8rpx;
            }
          }
          .text_36 {
            width: 62rpx;
            height: 24rpx;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 24rpx;
            margin: 8rpx 26rpx 0 12rpx;
          }
        }
      }
    }
    .text-wrapper_16 {
      width: 236rpx;
      height: 24rpx;
      margin: 682rpx 0 574rpx 444rpx;
      .text_37 {
        width: 48rpx;
        height: 24rpx;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 24rpx;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 24rpx;
      }
      .text_38 {
        width: 48rpx;
        height: 24rpx;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 24rpx;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 24rpx;
      }
    }
    .group_21 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 0px 0px 5px 5px;
      position: absolute;
      left: 24rpx;
      top: 1322rpx;
      width: 340rpx;
      height: 286rpx;
      .box_17 {
        width: 202rpx;
        height: 34rpx;
        margin: 20rpx 0 0 22rpx;
        .text_39 {
          width: 92rpx;
          height: 30rpx;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 30rpx;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 30rpx;
        }
        .text-wrapper_17 {
          background-color: rgba(30, 30, 30, 1);
          border-radius: 50px;
          height: 32rpx;
          margin-top: 2rpx;
          width: 94rpx;
          .text_40 {
            width: 66rpx;
            height: 16rpx;
            overflow-wrap: break-word;
            color: rgba(234, 225, 196, 1);
            font-size: 20rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 20rpx;
            margin: 6rpx 0 0 14rpx;
          }
        }
      }
      .image-text_11 {
        width: 146rpx;
        height: 24rpx;
        margin: 56rpx 0 0 24rpx;
        .thumbnail_10 {
          width: 18rpx;
          height: 18rpx;
          margin-top: 4rpx;
        }
        .text-group_11 {
          width: 122rpx;
          height: 24rpx;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 24rpx;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 24rpx;
        }
      }
      .box_18 {
        width: 292rpx;
        height: 54rpx;
        margin: 22rpx 0 0 26rpx;
        .text-wrapper_18 {
          border-radius: 4px;
          height: 54rpx;
          border: 1px solid rgba(11, 206, 148, 1);
          width: 134rpx;
          .text_41 {
            width: 102rpx;
            height: 30rpx;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
            margin: 14rpx 0 0 16rpx;
          }
        }
        .text-wrapper_19 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 5px;
          height: 54rpx;
          width: 134rpx;
          .text_42 {
            width: 98rpx;
            height: 24rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
            margin: 14rpx 0 0 22rpx;
          }
        }
      }
      .box_19 {
        width: 292rpx;
        height: 28rpx;
        margin: 34rpx 0 14rpx 26rpx;
        .block_4 {
          background-color: rgba(150, 150, 150, 1);
          width: 32rpx;
          height: 28rpx;
        }
        .text_43 {
          width: 16rpx;
          height: 24rpx;
          overflow-wrap: break-word;
          color: rgba(150, 150, 150, 1);
          font-size: 24rpx;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 24rpx;
          margin: 2rpx 0 0 6rpx;
        }
        .block_5 {
          background-color: rgba(150, 150, 150, 1);
          width: 30rpx;
          height: 28rpx;
          margin-left: 50rpx;
        }
        .text_44 {
          width: 16rpx;
          height: 24rpx;
          overflow-wrap: break-word;
          color: rgba(150, 150, 150, 1);
          font-size: 24rpx;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 24rpx;
          margin: 2rpx 0 0 6rpx;
        }
        .image-text_12 {
          width: 84rpx;
          height: 24rpx;
          margin: 2rpx 0 0 52rpx;
          .box_20 {
            background-color: rgba(150, 150, 150, 1);
            width: 26rpx;
            height: 24rpx;
          }
          .text-group_12 {
            width: 50rpx;
            height: 24rpx;
            overflow-wrap: break-word;
            color: rgba(150, 150, 150, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
          }
        }
      }
    }
    .group_22 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 0px 0px 5px 5px;
      position: absolute;
      left: 386rpx;
      top: 1322rpx;
      width: 340rpx;
      height: 286rpx;
      .group_23 {
        width: 202rpx;
        height: 34rpx;
        margin: 20rpx 0 0 22rpx;
        .text_45 {
          width: 92rpx;
          height: 30rpx;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 30rpx;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 30rpx;
        }
        .text-wrapper_20 {
          background-color: rgba(30, 30, 30, 1);
          border-radius: 50px;
          height: 32rpx;
          margin-top: 2rpx;
          width: 94rpx;
          .text_46 {
            width: 66rpx;
            height: 16rpx;
            overflow-wrap: break-word;
            color: rgba(234, 225, 196, 1);
            font-size: 20rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 20rpx;
            margin: 6rpx 0 0 14rpx;
          }
        }
      }
      .image-text_13 {
        width: 146rpx;
        height: 24rpx;
        margin: 56rpx 0 0 24rpx;
        .thumbnail_11 {
          width: 18rpx;
          height: 18rpx;
          margin-top: 4rpx;
        }
        .text-group_13 {
          width: 122rpx;
          height: 24rpx;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 24rpx;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 24rpx;
        }
      }
      .group_24 {
        width: 292rpx;
        height: 54rpx;
        margin: 22rpx 0 0 26rpx;
        .text-wrapper_21 {
          border-radius: 4px;
          height: 54rpx;
          border: 1px solid rgba(11, 206, 148, 1);
          width: 134rpx;
          .text_47 {
            width: 102rpx;
            height: 30rpx;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
            margin: 14rpx 0 0 16rpx;
          }
        }
        .text-wrapper_22 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 5px;
          height: 54rpx;
          width: 134rpx;
          .text_48 {
            width: 98rpx;
            height: 24rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
            margin: 14rpx 0 0 22rpx;
          }
        }
      }
      .group_25 {
        width: 292rpx;
        height: 28rpx;
        margin: 34rpx 0 14rpx 26rpx;
        .box_21 {
          background-color: rgba(150, 150, 150, 1);
          width: 32rpx;
          height: 28rpx;
        }
        .text_49 {
          width: 16rpx;
          height: 24rpx;
          overflow-wrap: break-word;
          color: rgba(150, 150, 150, 1);
          font-size: 24rpx;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 24rpx;
          margin: 2rpx 0 0 6rpx;
        }
        .box_22 {
          background-color: rgba(150, 150, 150, 1);
          width: 30rpx;
          height: 28rpx;
          margin-left: 50rpx;
        }
        .text_50 {
          width: 16rpx;
          height: 24rpx;
          overflow-wrap: break-word;
          color: rgba(150, 150, 150, 1);
          font-size: 24rpx;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 24rpx;
          margin: 2rpx 0 0 6rpx;
        }
        .image-text_14 {
          width: 84rpx;
          height: 24rpx;
          margin: 2rpx 0 0 52rpx;
          .group_26 {
            background-color: rgba(150, 150, 150, 1);
            width: 26rpx;
            height: 24rpx;
          }
          .text-group_14 {
            width: 50rpx;
            height: 24rpx;
            overflow-wrap: break-word;
            color: rgba(150, 150, 150, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
          }
        }
      }
    }
    .text-wrapper_23 {
      position: absolute;
      left: -790rpx;
      top: 1768rpx;
      width: 336rpx;
      height: 336rpx;
      background: url(/static/lanhu_shouyejishikapian/FigmaDDSSlicePNGa911c964ed32937659ea639dfb0c05a2.png)
        100% no-repeat;
      background-size: 100% 100%;
      .text_51 {
        width: 48rpx;
        height: 24rpx;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 24rpx;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 24rpx;
        margin: 236rpx 0 0 860rpx;
      }
      .text_52 {
        width: 48rpx;
        height: 24rpx;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 24rpx;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 24rpx;
        margin: 236rpx -758rpx 0 138rpx;
      }
    }
    .text-wrapper_24 {
      background-color: rgba(16, 16, 16, 0.1);
      border-radius: 100px;
      height: 46rpx;
      width: 140rpx;
      position: absolute;
      left: 656rpx;
      top: 202rpx;
      .text_53 {
        width: 104rpx;
        height: 38rpx;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 26rpx;
        font-family: Source Han Sans CN-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26rpx;
        margin: 4rpx 0 0 18rpx;
      }
    }
    .group_27 {
      background-color: rgba(255, 255, 255, 1);
      position: absolute;
      left: 0;
      top: 1354rpx;
      width: 750rpx;
      height: 98rpx;
      .image-text_15 {
        width: 48rpx;
        height: 78rpx;
        margin-top: 12rpx;
        .label_2 {
          width: 44rpx;
          height: 44rpx;
          margin-left: 2rpx;
        }
        .text-group_15 {
          width: 48rpx;
          height: 24rpx;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
          margin-top: 10rpx;
        }
      }
      .image-text_16 {
        width: 48rpx;
        height: 76rpx;
        margin-top: 14rpx;
        .block_6 {
          height: 44rpx;
          background: url(/static/lanhu_shouyejishikapian/a1163f772a4e46fcade03d5393284bde_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 2rpx;
          width: 44rpx;
          .group_28 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 50%;
            width: 14rpx;
            height: 14rpx;
            margin: 6rpx 0 0 20rpx;
          }
        }
        .text-group_16 {
          width: 48rpx;
          height: 24rpx;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
          margin-top: 8rpx;
        }
      }
      .image-text_17 {
        width: 48rpx;
        height: 76rpx;
        margin-top: 14rpx;
        .label_3 {
          width: 44rpx;
          height: 44rpx;
          margin-left: 2rpx;
        }
        .text-group_17 {
          width: 48rpx;
          height: 24rpx;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
          margin-top: 8rpx;
        }
      }
      .image-text_18 {
        width: 48rpx;
        height: 76rpx;
        margin-top: 14rpx;
        .label_4 {
          width: 44rpx;
          height: 44rpx;
          margin-left: 2rpx;
        }
        .text-group_18 {
          width: 48rpx;
          height: 24rpx;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
          margin-top: 8rpx;
        }
      }
    }
    .text-wrapper_25 {
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: 5px;
      height: 68rpx;
      width: 188rpx;
      position: absolute;
      left: 138rpx;
      top: 972rpx;
      .text_54 {
        width: 112rpx;
        height: 28rpx;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 28rpx;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 28rpx;
        margin: 20rpx 0 0 38rpx;
      }
    }
  }
}

// 旧的地图容器样式已移除，使用下方的新样式

// 技师列表栅格布局
.technician-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 45rpx 24rpx 0 24rpx;
  gap: 24rpx;
  // 添加进入动画
  animation: fadeInUp 0.5s ease-out;
}

.technician-card {
  width: 340rpx;        // 固定宽度 170px * 2 = 340rpx
  min-width: 340rpx;    // 强制最小宽度
  max-width: 340rpx;    // 强制最大宽度
  flex: 0 0 340rpx;     // 不允许伸缩，固定基础尺寸
  margin-bottom: 22rpx;
  // 添加卡片动画
  animation: slideInUp 0.6s ease-out;
  animation-fill-mode: both;

  // 为每个卡片添加延迟动画
  &:nth-child(1) { animation-delay: 0.1s; }
  &:nth-child(2) { animation-delay: 0.2s; }
  &:nth-child(3) { animation-delay: 0.3s; }
  &:nth-child(4) { animation-delay: 0.4s; }

  // 技师头像背景区域
  .card-avatar {
    height: 336rpx;
    width: 100%;
    position: relative;
    overflow: hidden; // 确保图片不会溢出
    border-radius: 5px 5px 0px 0px; // 顶部圆角

    // 技师头像图片
    .technician-avatar {
      width: 100%;
      height: 100%;
      object-fit: cover; // 保持图片比例并填充容器
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1; // 确保在背景层
    }

    // 最早可约时间标签
    .time-badge {
      position: absolute;
      bottom: 22rpx;
      left: 50rpx;
      background-color: rgba(255, 255, 255, 1);
      border-radius: 20px;
      width: 222rpx;
      height: 42rpx;
      display: flex;
      align-items: center;
      z-index: 2; // 确保显示在头像图片之上

      .time-label-wrapper {
        background-color: rgba(11, 206, 148, 1);
        border-radius: 20px;
        height: 42rpx;
        width: 122rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .time-label {
          color: rgba(255, 255, 255, 1);
          font-size: 24rpx;
          font-family: Source Han Sans CN-Regular;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
        }
      }

      .time-value {
        color: rgba(11, 206, 148, 1);
        font-size: 24rpx;
        font-family: Source Han Sans CN-Regular;
        text-align: center;
        white-space: nowrap;
        line-height: 24rpx;
        margin-left: 16rpx;
      }
    }
  }

  // 技师信息卡片
  .card-content {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 0px 0px 5px 5px;
    width: 100%;
    height: 286rpx;

    // 技师姓名和状态
    .technician-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx 22rpx 0 22rpx;

      .technician-name {
        color: rgba(0, 0, 0, 1);
        font-size: 30rpx;
        font-family: Source Han Sans CN-Regular;
        text-align: left;
        white-space: nowrap;
        line-height: 30rpx;
      }

      .status-badge {
        background-color: rgba(30, 30, 30, 1);
        border-radius: 50px;
        height: 32rpx;
        width: 94rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .status-text {
          color: rgba(234, 225, 196, 1);
          font-size: 20rpx;
          font-family: Source Han Sans CN-Regular;
          text-align: center;
          white-space: nowrap;
          line-height: 20rpx;
        }
      }
    }

    // 评分和服务次数
    .rating-section {
      display: flex;
      align-items: center;
      gap: 12rpx;  // 缩小间距
      padding: 16rpx 22rpx 0 22rpx;

      .rating-star {
        width: 22rpx;   // 11.3px * 2 ≈ 22rpx
        height: 22rpx;  // 11px * 2 = 22rpx
        background: url(/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG751b001b58a95440d7eae2a8c0959d9d.png) no-repeat center;
        background-size: contain;
      }

      .service-info {
        display: flex;
        gap: 8rpx;  // 缩小内部间距
        align-items: center;

        .rating-score {
          color: rgba(255, 193, 7, 1);  // 黄色评分数字
          font-size: 24rpx;
          font-family: Source Han Sans CN-Regular;
          font-weight: 500;
        }

        .service-count {
          color: rgba(153, 153, 153, 1);
          font-size: 24rpx;
          font-family: Source Han Sans CN-Regular;
        }
      }
    }

    // 出行费用
    .travel-fee {
      display: flex;
      align-items: center;
      padding: 16rpx 22rpx 0 22rpx;

      .fee-icon {
        width: 16rpx;   // 8.1px * 2 ≈ 16rpx
        height: 18rpx;  // 9px * 2 = 18rpx
        margin-right: 12rpx;
      }

      .fee-text {
        color: rgba(0, 200, 150, 1);
        font-size: 26rpx;
        font-family: Source Han Sans CN-Regular;
      }
    }

    // 操作按钮
    .action-buttons {
      display: flex;
      justify-content: space-between;
      gap: 16rpx;
      padding: 20rpx 22rpx 0 22rpx;

      .btn-secondary {
        flex: 1;
        background-color: rgba(248, 248, 248, 1);
        border-radius: 8rpx;
        padding: 6rpx 12rpx;  // 减少内边距
        text-align: center;
        height: 48rpx;        // 减小高度
        display: flex;
        align-items: center;
        justify-content: center;

        .btn-text {
          color: rgba(102, 102, 102, 1);
          font-size: 22rpx;    // 稍微减小字体
          font-family: Source Han Sans CN-Regular;
          line-height: 1;
        }
      }

      .btn-primary {
        flex: 1;
        background-color: rgba(11, 206, 148, 1);
        border-radius: 8rpx;
        padding: 6rpx 12rpx;  // 减少内边距
        text-align: center;
        height: 48rpx;        // 减小高度
        display: flex;
        align-items: center;
        justify-content: center;

        .btn-text {
          color: rgba(255, 255, 255, 1);
          font-size: 22rpx;    // 稍微减小字体
          font-family: Source Han Sans CN-Regular;
          line-height: 1;
        }
      }
    }

    // 底部图标信息
    .bottom-info {
      display: flex;
      justify-content: space-between;
      padding: 16rpx 22rpx 20rpx 22rpx;

      .info-item {
        display: flex;
        align-items: center;
        gap: 8rpx;

        .info-text {
          color: rgba(153, 153, 153, 1);
          font-size: 24rpx;
          font-family: Source Han Sans CN-Regular;
        }
      }
    }
  }
}

// 一行一列技师列表样式
.technician-list-container {
  width: 702rpx;
  justify-content: space-between;
  margin: 45rpx 0 0 24rpx;
  animation: fadeInLeft 0.5s ease-out;

  .technician-list-item {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 10px;
    height: 232rpx;
    margin-bottom: 20rpx;
    width: 702rpx;
    animation: slideInLeft 0.6s ease-out;
    animation-fill-mode: both;

    &:nth-child(1) { animation-delay: 0.1s; }
    &:nth-child(2) { animation-delay: 0.2s; }
    &:nth-child(3) { animation-delay: 0.3s; }
    &:nth-child(4) { animation-delay: 0.4s; }

    .technician-info-top {
      width: 654rpx;
      height: 124rpx;
      margin: 28rpx 0 0 24rpx;

      .technician-avatar-img {
        width: 124rpx;
        height: 124rpx;
      }

      .single-row-image {
        width: 202rpx;
        height: 78rpx;
        margin-left: 24rpx;

        .technician-name-row {
          width: 202rpx;
          height: 32rpx;

          .technician-name-text {
            width: 92rpx;
            height: 32rpx;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 30rpx;
            font-family: Source Han Sans CN-Regular;
            text-align: left;
            white-space: nowrap;
            line-height: 30rpx;
          }

          .technician-photos-btn {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 2px;
            height: 32rpx;
            width: 100rpx;

            .technician-photos-text {
              width: 80rpx;
              height: 20rpx;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 20rpx;
              font-family: PingFang SC-Regular;
              text-align: left;
              white-space: nowrap;
              line-height: 20rpx;
              margin: 6rpx 0 0 10rpx;
            }
          }
        }

        .technician-rating-row {
          width: 192rpx;
          height: 24rpx;
          margin-top: 22rpx;

          .technician-rating-area {
            width: 46rpx;
            height: 24rpx;

            .technician-star-icon {
              width: 22rpx;
              height: 22rpx;
              background: url(/static/lanhu_shouyejishiliebiao_2/FigmaDDSSlicePNGc1058156914a6b7b77a1b51a9fa717c0.png) 100% no-repeat;
              background-size: 100% 100%;
              margin-top: 2rpx;
            }

            .technician-rating-text {
              width: 14rpx;
              height: 24rpx;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 24rpx;
              font-family: Source Han Sans CN-Regular;
              text-align: left;
              white-space: nowrap;
              line-height: 24rpx;
            }
          }

          .technician-service-text {
            width: 136rpx;
            height: 24rpx;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
            margin-left: 30rpx;
          }
        }
      }

      .single-row-time {
        width: 172rpx;
        height: 78rpx;
        margin-left: 132rpx;

        .technician-time-wrapper {
          background-color: rgba(62, 200, 174, 0.2);
          border-radius: 2px;
          height: 32rpx;
          width: 172rpx;

          .technician-time-text {
            width: 160rpx;
            height: 20rpx;
            overflow-wrap: break-word;
            color: rgba(62, 200, 174, 1);
            font-size: 20rpx;
            font-family: PingFang SC-Regular;
            text-align: left;
            white-space: nowrap;
            line-height: 20rpx;
            margin: 6rpx 0 0 6rpx;
          }
        }

        .technician-distance-area {
          width: 110rpx;
          height: 26rpx;
          margin: 20rpx 0 0 60rpx;

          .single-row-distance {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 22rpx;
            height: 26rpx;
          }

          .technician-distance-text {
            width: 84rpx;
            height: 24rpx;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 24rpx;
            font-family: PingFang SC-Regular;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
            margin-top: 2rpx;
          }
        }
      }
    }

    .technician-info-bottom {
      width: 642rpx;
      height: 52rpx;
      margin: 8rpx 0 20rpx 36rpx;
      position: relative;

      .technician-status-badge {
        border-radius: 100px;
        height: 32rpx;
        margin-top: 8rpx;
        min-width: 100rpx;
        padding: 0 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        left: 50rpx;
        transform: translateX(-50%);

        .technician-status-text {
          color: rgba(255, 255, 255, 1);
          font-size: 20rpx;
          font-family: PingFang SC-Regular;
          text-align: center;
          white-space: nowrap;
          line-height: 20rpx;
        }
      }

      .bottom-info {
        display: flex;
        justify-content: space-between;
        padding: 16rpx 22rpx 20rpx 22rpx;
        gap: 30rpx;
        margin-left: 120rpx;

        .info-item {
          display: flex;
          align-items: center;
          gap: 8rpx;
          flex: 1;

          .info-text {
            color: rgba(153, 153, 153, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
          }
        }
      }

      .technician-book-btn {
        background-color: rgba(11, 206, 148, 1);
        border-radius: 30px;
        height: 52rpx;
        margin-left: 110rpx;
        width: 132rpx;

        .technician-book-text {
          width: 96rpx;
          height: 24rpx;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          text-align: left;
          white-space: nowrap;
          line-height: 24rpx;
          margin: 14rpx 0 0 18rpx;
        }
      }
    }
  }
}

// 地图视图样式
.map-container {
  width: 100%;
  // 确保地图容器有明确的高度
  height: 600px; // 使用px单位确保在H5环境下正确显示
  min-height: 400px; // 最小高度
  position: relative;
  margin: 0; // 移除边距
  border-radius: 0; // 移除圆角
  overflow: hidden;
  background-color: #f0f0f0; // 添加背景色便于调试

  // 添加平滑的显示/隐藏动画
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center top;

  // 当激活动画时
  &.map-animate {
    animation: mapSlideIn 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }

  // 当隐藏时的样式
  &[style*="display: none"] {
    animation: mapSlideOut 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }

  // H5环境下的特殊样式
  &.h5-fullscreen {
    height: 600px; // 固定高度，避免calc计算问题
    min-height: 400px; // 最小高度保证
    max-height: 80vh; // 最大高度限制

    // 响应式设计
    @media (max-height: 600px) {
      height: 400px;
      min-height: 300px;
    }

    @media (max-height: 500px) {
      height: 350px;
      min-height: 250px;
    }
  }

  .technician-map {
    width: 100%;
    height: 100%;
    border-radius: 0; // 移除圆角
    // 地图内容延迟淡入
    animation: mapContentFadeIn 0.8s ease-out 0.2s both;
  }

  // H5地图样式
  .h5-map-wrapper {
    width: 100%;
    height: 100%;
    position: relative;
    border-radius: 0; // 移除圆角
    overflow: hidden;
    // 地图内容延迟淡入
    animation: mapContentFadeIn 0.8s ease-out 0.2s both;

    .leaflet-map {
      width: 100%;
      height: 100%;
      border-radius: 0; // 移除圆角
      overflow: hidden;
    }


  }

  // 地图加载状态
  .map-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 100;
    background: rgba(255, 255, 255, 0.9);
    padding: 20rpx 40rpx;
    border-radius: 10rpx;

    .loading-text {
      font-size: 28rpx;
      color: #666;
    }
  }

  // 地图控制按钮
  .map-controls {
    position: absolute;
    right: 20rpx;
    top: 20rpx;
    z-index: 50;
    display: flex;
    flex-direction: column;
    gap: 10rpx;

    .control-btn {
      width: 80rpx;
      height: 80rpx;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 1);
        transform: scale(1.1);
      }

      .control-icon {
        font-size: 32rpx;
        color: #333;
      }
    }
  }

  // 自定义标记点样式（当前隐藏，使用原生标记）
  .custom-marker {
    position: absolute;
    z-index: 10;

    .marker-avatar-container {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      border: 4rpx solid #ffffff;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
      overflow: hidden;
      background-color: #ffffff;

      .marker-avatar {
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }
    }

    .marker-distance-tag {
      position: absolute;
      top: -10rpx;
      right: -20rpx;
      background-color: #0BCE94;
      border-radius: 20rpx;
      padding: 8rpx 16rpx;
      min-width: 100rpx;
      box-shadow: 0 2rpx 8rpx rgba(11, 206, 148, 0.3);

      .marker-name {
        color: #ffffff;
        font-size: 20rpx;
        font-weight: 500;
        display: block;
        text-align: center;
        line-height: 24rpx;
      }

      .marker-distance {
        color: #ffffff;
        font-size: 18rpx;
        display: block;
        text-align: center;
        line-height: 20rpx;
        margin-top: 2rpx;
      }
    }
  }
}

// Leaflet自定义标记样式（全局样式，不受scoped限制）
:global(.custom-marker) {
  background: transparent !important;
  border: none !important;

  .marker-container {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 6px; // 从8px调小到6px，更紧凑
    padding: 2px 4px; // 调整内边距，左右稍大，上下更小
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15); // 调小阴影
    cursor: pointer;
    transition: all 0.3s ease;
    max-width: 100px; // 限制最大宽度，配合iconSize调整

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    }

    .marker-avatar {
      width: 24px !important; // 从30px调小到24px，使用!important确保优先级
      height: 24px !important; // 从30px调小到24px，使用!important确保优先级
      border-radius: 50% !important; // 确保圆形
      margin-right: 4px; // 调小间距
      object-fit: cover !important; // 确保图片填充且不变形
      border: 1px solid #fff !important; // 添加白色边框增强视觉效果
    }

    .marker-info {
      display: flex;
      flex-direction: column;

      .marker-name {
        font-size: 11px; // 从12px调小到11px
        font-weight: 500;
        color: #333;
        margin-bottom: 2px;
        white-space: nowrap;
      }

      .marker-distance {
        font-size: 9px; // 从10px调小到9px
        color: #52c41a;
        font-weight: 500;
        white-space: nowrap;
      }
    }
  }
}

// 选中技师卡片样式
.selected-technician-card {
  position: fixed;
  bottom: 120rpx; // 在底部导航上方
  left: 24rpx;
  right: 24rpx;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 20rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10rpx);
  padding: 20rpx;

  .technician-list-item {
    background-color: transparent;
    margin-bottom: 0;
    width: 100%;
    height: auto;
    border-radius: 0;

    .technician-info-top {
      width: 100%;
      height: 124rpx;
      margin: 0;
      display: flex;
      flex-direction: row;
      align-items: flex-start;

      .technician-avatar-img {
        width: 124rpx;
        height: 124rpx;
        border-radius: 10rpx;
        flex-shrink: 0;
      }

      .single-row-image {
        width: auto;
        height: auto;
        margin-left: 24rpx;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .technician-name-row {
          width: 100%;
          height: auto;
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;

          .technician-name-text {
            width: auto;
            height: auto;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 30rpx;
            font-family: Source Han Sans CN-Regular;
            text-align: left;
            white-space: nowrap;
            line-height: 30rpx;
            flex: 1;
          }

          .technician-photos-btn {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 2px;
            height: 32rpx;
            width: 100rpx;
            display: flex;
            align-items: center;
            justify-content: center;

            .technician-photos-text {
              color: rgba(255, 255, 255, 1);
              font-size: 20rpx;
              font-family: PingFang SC-Regular;
              text-align: center;
              white-space: nowrap;
              line-height: 20rpx;
            }
          }
        }

        .technician-rating-row {
          width: 100%;
          height: auto;
          margin-top: 22rpx;
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;

          .technician-rating-area {
            width: auto;
            height: 24rpx;
            display: flex;
            flex-direction: row;
            align-items: center;

            .technician-star-icon {
              width: 22rpx;
              height: 22rpx;
              background: url(/static/lanhu_shouyejishiliebiao_2/FigmaDDSSlicePNGc1058156914a6b7b77a1b51a9fa717c0.png) 100% no-repeat;
              background-size: 100% 100%;
              margin-right: 10rpx;
            }

            .technician-rating-text {
              color: rgba(102, 102, 102, 1);
              font-size: 24rpx;
              font-family: Source Han Sans CN-Regular;
              text-align: left;
              white-space: nowrap;
              line-height: 24rpx;
            }
          }

          .technician-service-text {
            color: rgba(102, 102, 102, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            text-align: right;
            white-space: nowrap;
            line-height: 24rpx;
          }
        }
      }

      .single-row-time {
        width: 172rpx;
        height: auto;
        margin-left: 20rpx;
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .technician-time-wrapper {
          background-color: rgba(62, 200, 174, 0.2);
          border-radius: 2px;
          height: 32rpx;
          width: 172rpx;
          display: flex;
          align-items: center;
          justify-content: center;

          .technician-time-text {
            color: rgba(62, 200, 174, 1);
            font-size: 20rpx;
            font-family: PingFang SC-Regular;
            text-align: center;
            white-space: nowrap;
            line-height: 20rpx;
          }
        }

        .technician-distance-area {
          width: 100%;
          height: 26rpx;
          margin-top: 20rpx;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: flex-end;

          .single-row-distance {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 22rpx;
            height: 26rpx;
            margin-right: 8rpx;
          }

          .technician-distance-text {
            color: rgba(102, 102, 102, 1);
            font-size: 24rpx;
            font-family: PingFang SC-Regular;
            text-align: right;
            white-space: nowrap;
            line-height: 24rpx;
          }
        }
      }
    }

    .technician-info-bottom {
      width: 100%;
      height: auto;
      margin: 20rpx 0 0 0;
      position: relative;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;

      .technician-status-badge {
        border-radius: 100px;
        height: 32rpx;
        min-width: 100rpx;
        padding: 0 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        position: static;
        transform: none;
        margin-right: 20rpx;

        .technician-status-text {
          color: rgba(255, 255, 255, 1);
          font-size: 20rpx;
          font-family: PingFang SC-Regular;
          text-align: center;
          white-space: nowrap;
          line-height: 20rpx;
        }
      }

      .bottom-info {
        display: flex;
        justify-content: space-between;
        gap: 30rpx;
        flex: 1;
        margin: 0 20rpx;

        .info-item {
          display: flex;
          align-items: center;
          gap: 8rpx;
          flex: 1;

          .info-text {
            color: rgba(153, 153, 153, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
          }
        }
      }

      .technician-book-btn {
        background-color: rgba(11, 206, 148, 1);
        border-radius: 30px;
        height: 52rpx;
        width: 132rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0;

        .technician-book-text {
          color: rgba(255, 255, 255, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
        }
      }
    }
  }
}

// 卡片动画
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  transform: translateY(100%);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(100%);
  opacity: 0;
}

// 地图切换动画关键帧
@keyframes mapSlideIn {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes mapSlideOut {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(-20px) scale(0.98);
  }
}

// 地图内容淡入动画
@keyframes mapContentFadeIn {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
