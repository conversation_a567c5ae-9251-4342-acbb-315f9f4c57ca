<template>
  <scroll-view class="page-scroll" scroll-y="true" :style="{ height: '100vh' }">
    <view class="page flex-col">
    <view class="section_1 flex-col justify-between">
      <view class="box_1 flex-row">
        <text class="text_1">12:30</text>
        <image
          class="thumbnail_1"
          referrerpolicy="no-referrer"
          src="/static/lanhu_shouyejishikapian/FigmaDDSSlicePNGd7f3f0a8da68df76a531af0fb9fddfd7.png"
        />
        <image
          class="thumbnail_2"
          referrerpolicy="no-referrer"
          src="/static/lanhu_shouyejishikapian/FigmaDDSSlicePNG5386e23e17798b9096a433b239b703f0.png"
        />
        <image
          class="thumbnail_3"
          referrerpolicy="no-referrer"
          src="/static/lanhu_shouyejishikapian/FigmaDDSSlicePNG5abe3a2953abf107d379c43fded5e38a.png"
        />
      </view>
      <view class="box_2 flex-row justify-between">
        <text class="text_2">预约到家</text>
        <image
          class="image_1"
          referrerpolicy="no-referrer"
          src="/static/lanhu_shouyejishikapian/FigmaDDSSlicePNGb8b4476965cc16cde5ebd2a73f016520.png"
        />
      </view>
    </view>

    <view class="section_2 flex-col">
      <view class="group_1 flex-col">
        
        <view class="box_3 flex-row">
          <!-- 定位 地图/列表切换 -->
          <image
            class="thumbnail_4"
            referrerpolicy="no-referrer"
            src="/static/lanhu_shouyejishikapian/FigmaDDSSlicePNG9054b3373ab26e98fe31e2bc2cf29a06.png"
            @click="handleLocationClick"
          />
          <view class="image-text_1 flex-row justify-between">
            <text class="text-group_1">{{ currentAddress }}</text>
            <view class="box_4 flex-col">
              <image
                class="vector-icon"
                referrerpolicy="no-referrer"
                src="/static/images/vector.png"
              />
            </view>
          </view>
          <!-- 定位 地图/列表切换 -->

          <!--tabs切换-->
          <view class="block_1 flex-row" :class="{ 'tab-active': tabsIndex === 0 }" @click="switchTab(0)">
            <view class="image-text_2 flex-row justify-between">
              <image
                class="thumbnail_5"
                referrerpolicy="no-referrer"
                src="/static/lanhu_shouyejishikapian/FigmaDDSSlicePNG615283e6802551177c13b8cbed080173.png"
              />
              <text class="text-group_2">地图</text>
            </view>
          </view>
          <view class="block_2 flex-row" :class="{ 'tab-active': tabsIndex === 1 }" @click="switchTab(1)">
            <view class="image-text_3 flex-row justify-between">
              <image
                class="thumbnail_6"
                referrerpolicy="no-referrer"
                src="/static/lanhu_shouyejishikapian/FigmaDDSSlicePNGb180c0de46a11117e51b349c43520f5f.png"
              />
              <text class="text-group_3">列表</text>
            </view>
          </view>
          <!--tabs切换-->

        </view>
        <view class="box_5 flex-row">
          <view class="image-text_4 flex-row justify-between">
            <text class="text-group_4">默认城市</text>
            <view class="group_2 flex-col">
              <image
                class="vector-icon"
                referrerpolicy="no-referrer"
                src="/static/images/vector.png"
              />
            </view>
          </view>
          <text class="text_3">请输入要查找的项目</text>
          <image
            class="label_1"
            referrerpolicy="no-referrer"
            src="/static/lanhu_shouyejishikapian/FigmaDDSSlicePNG444a7b67873a94e8a3152bb485a1dba9.png"
          />
        </view>

        <!-- 服务项目水平滚动列表 - 开始 -->
        <view class="box_6">
          <view class="scroll-content flex-row">
            <view
              v-for="item in serviceItems"
              :key="item.id"
              class="service-item"
            >
              <text class="service-text">{{ item.text }}</text>
            </view>
          </view>
        </view>
        <!-- 服务项目水平滚动列表 - 结束 -->

        <view class="box_7 flex-row">
          <text class="text_8">服务类型</text>
          <view class="group_3 flex-col">
            <image
              class="vector-icon"
              referrerpolicy="no-referrer"
              src="/static/images/vector.png"
            />
          </view>
          <text class="text_9">技师性别</text>
          <view class="group_4 flex-col">
            <image
              class="vector-icon"
              referrerpolicy="no-referrer"
              src="/static/images/vector.png"
            />
          </view>
          <text class="text_10">技师年龄</text>
          <view class="group_5 flex-col">
            <image
              class="vector-icon"
              referrerpolicy="no-referrer"
              src="/static/images/vector.png"
            />
          </view>
          <text class="text_11">服务状态</text>
          <view class="group_6 flex-col">
            <image
              class="vector-icon"
              referrerpolicy="no-referrer"
              src="/static/images/vector.png"
            />
          </view>
        </view>
      </view>

      <!--地图视图-->
      <view
        class="map-container"
        :class="{
          'h5-fullscreen': isH5,
          'map-animate': mapAnimating
        }"
        v-show="tabsIndex === 0"
      >
        <!-- H5环境下使用Leaflet地图 -->
        <view class="h5-map-wrapper" v-if="isH5" @click="onMapClick">
          <view id="leafletMapContainer" class="leaflet-map"></view>
        </view>

        <!-- 非H5环境使用uni-app map组件 -->
        <map
          v-else
          id="technicianMap"
          class="technician-map"
          :latitude="mapCenter.latitude"
          :longitude="mapCenter.longitude"
          :scale="mapScale"
          :markers="mapMarkers"
          :show-location="false"
          @markertap="onMarkerTap"
          @regionchange="onRegionChange"
          @tap="onMapClick"
        />

        <!-- 地图加载状态提示 -->
        <view class="map-loading" v-if="!mapLoaded">
          <text class="loading-text">地图加载中...</text>
        </view>
      </view>

      <!--技师列表 栅格一行两列-->
      <transition name="fade-slide" mode="out-in">
        <view class="technician-grid" v-if="tabsIndex === 1 && layoutMode === 0" key="grid">
        <view class="technician-card" v-for="technician in technicianList" :key="technician.id">
          <!-- 技师头像背景区域 -->
          <view class="card-avatar">
            <!-- 技师头像图片 -->
            <image
              class="technician-avatar"
              referrerpolicy="no-referrer"
              :src="technician.avatar"
            />
            <view class="time-badge">
              <view class="time-label-wrapper">
                <text class="time-label">最早可约</text>
              </view>
              <text class="time-value">{{ technician.earliestTime }}</text>
            </view>
          </view>

          <!-- 技师信息卡片 -->
          <view class="card-content">
            <!-- 技师姓名和状态 -->
            <view class="technician-header">
              <text class="technician-name">{{ technician.name }}</text>
              <view class="status-badge">
                <text class="status-text">{{ technician.status }}</text>
              </view>
            </view>

            <!-- 评分和服务次数 -->
            <view class="rating-section">
              <view class="rating-star"></view>
              <view class="service-info">
                <text class="rating-score">{{ technician.rating }}</text>
                <text class="service-count">已服务{{ technician.serviceCount }}单</text>
              </view>
            </view>

            <!-- 出行费用 -->
            <view class="travel-fee">
              <image
                class="fee-icon"
                referrerpolicy="no-referrer"
                :src="technician.freeIcon"
              />
              <text class="fee-text">{{ technician.freeTravel ? '免出行费用' : '需出行费用' }}</text>
            </view>

            <!-- 操作按钮 -->
            <view class="action-buttons">
              <view class="btn-secondary">
                <text class="btn-text">更多照片</text>
              </view>
              <view class="btn-primary">
                <text class="btn-text">立即预约</text>
              </view>
            </view>

            <!-- 底部图标信息 -->
            <view class="bottom-info">
              <view class="info-item">
                <uni-icons type="chat" size="16" color="#969696"></uni-icons>
                <text class="info-text">{{ technician.comments }}</text>
              </view>
              <view class="info-item">
                <uni-icons type="star" size="16" color="#969696"></uni-icons>
                <text class="info-text">{{ technician.favorites }}</text>
              </view>
              <view class="info-item">
                <uni-icons type="shop" size="16" color="#969696"></uni-icons>
                <text class="info-text">{{ technician.shopType }}</text>
              </view>
            </view>
          </view>
        </view>
        </view>
      </transition>
      <!--技师列表 栅格一行两列-->

      <!--技师列表 栅格一行一列-->
      <transition name="fade-slide" mode="out-in">
        <view class="technician-list-container flex-col" v-if="tabsIndex === 1 && layoutMode === 1" key="list">
        <view
          class="technician-list-item flex-col"
          v-for="(item, index) in technicianList"
          :key="index"
        >
          <view class="technician-info-top flex-row">
            <image
              class="technician-avatar-img"
              referrerpolicy="no-referrer"
              :src="item.avatar"
            />
            <view class="single-row-image flex-col justify-between">
              <view class="technician-name-row flex-row justify-between">
                <text class="technician-name-text" >{{ item.name }}</text>
                <view class="technician-photos-btn flex-col">
                  <text class="technician-photos-text" > 更多照片 </text>
                </view>
              </view>
              <view class="technician-rating-row flex-row justify-between">
                <view class="technician-rating-area flex-row justify-between">
                  <view class="technician-star-icon flex-col"></view>
                  <text class="technician-rating-text" >{{ item.rating }}</text>
                </view>
                <text class="technician-service-text" >已服务{{item.serviceCount}}单</text>
              </view>
            </view>
            <view class="single-row-time flex-col justify-between">
              <view class="technician-time-wrapper flex-col">
                <text class="technician-time-text" >最早可约：{{item.earliestTime}}</text>
              </view>
              <view class="technician-distance-area flex-row justify-between">
                <view class="single-row-distance flex-col">
                  <uni-icons type="location" size="16" color="#0BCE94"></uni-icons>
                </view>
                <text class="technician-distance-text" >{{item.distance}}</text>
              </view>
            </view>
          </view>
          <view class="technician-info-bottom flex-row">
            <view
              class="technician-status-badge flex-col"
              :style="{ background: item.lanhuBg13 }"
            >
              <text class="technician-status-text" >{{ item.status }}</text>
            </view>
            <view class="bottom-info">
              <!--评论-->
              <view class="info-item">
                <uni-icons type="chat" size="20" color="#969696"></uni-icons>
                <text class="info-text">{{ item.comments }}</text>
              </view>

              <!--收藏-->
              <view class="info-item">
                <uni-icons type="star" size="20" color="#969696"></uni-icons>
                <text class="info-text">{{ item.favorites }}</text>
              </view>

              <!--商家-->
              <view class="info-item">
                <uni-icons type="shop" size="20" color="#969696"></uni-icons>
                <text class="info-text">{{ item.shopType }}</text>
              </view>
            </view>
            <view class="technician-book-btn flex-col">
              <text class="technician-book-text" >立即预约</text>
            </view>
          </view>
        </view>
        </view>
      </transition>
      <!--技师列表 栅格一行一列-->

      <!-- 地图选中技师卡片 -->
      <transition name="slide-up">
        <view class="selected-technician-card" v-if="selectedTechnician && tabsIndex === 0">
          <view class="technician-list-item flex-col">
            <view class="technician-info-top flex-row">
              <image
                class="technician-avatar-img"
                referrerpolicy="no-referrer"
                :src="selectedTechnician.avatar"
              />
              <view class="single-row-image flex-col justify-between">
                <view class="technician-name-row flex-row justify-between">
                  <text class="technician-name-text">{{ selectedTechnician.name }}</text>
                  <view class="technician-photos-btn flex-col">
                    <text class="technician-photos-text">更多照片</text>
                  </view>
                </view>
                <view class="technician-rating-row flex-row justify-between">
                  <view class="technician-rating-area flex-row justify-between">
                    <view class="technician-star-icon flex-col"></view>
                    <text class="technician-rating-text">{{ selectedTechnician.rating }}</text>
                  </view>
                  <text class="technician-service-text">已服务{{ selectedTechnician.serviceCount }}单</text>
                </view>
              </view>
              <view class="single-row-time flex-col justify-between">
                <view class="technician-time-wrapper flex-col">
                  <text class="technician-time-text">最早可约：{{ selectedTechnician.earliestTime }}</text>
                </view>
                <view class="technician-distance-area flex-row justify-between">
                  <view class="single-row-distance flex-col">
                    <uni-icons type="location" size="16" color="#0BCE94"></uni-icons>
                  </view>
                  <text class="technician-distance-text">{{ selectedTechnician.distance }}</text>
                </view>
              </view>
            </view>
            <view class="technician-info-bottom flex-row">
              <view
                class="technician-status-badge flex-col"
                :style="{ background: selectedTechnician.lanhuBg13 }"
              >
                <text class="technician-status-text">{{ selectedTechnician.status }}</text>
              </view>
              <view class="bottom-info">
                <!--评论-->
                <view class="info-item">
                  <uni-icons type="chat" size="20" color="#969696"></uni-icons>
                  <text class="info-text">{{ selectedTechnician.comments }}</text>
                </view>

                <!--收藏-->
                <view class="info-item">
                  <uni-icons type="star" size="20" color="#969696"></uni-icons>
                  <text class="info-text">{{ selectedTechnician.favorites }}</text>
                </view>

                <!--商家-->
                <view class="info-item">
                  <uni-icons type="shop" size="20" color="#969696"></uni-icons>
                  <text class="info-text">{{ selectedTechnician.shopType }}</text>
                </view>
              </view>
              <view class="technician-book-btn flex-col">
                <text class="technician-book-text">立即预约</text>
              </view>
            </view>
          </view>
        </view>
      </transition>
      <!-- 地图选中技师卡片 -->

      <view class="group_27 flex-row justify-around">
        <view class="image-text_15 flex-col justify-between">
          <image
            class="label_2"
            referrerpolicy="no-referrer"
            src="/static/lanhu_shouyejishikapian/FigmaDDSSlicePNG3a64844a13b7e0453d08677530393353.png"
          />
          <text class="text-group_15">首页</text>
        </view>
        <view class="image-text_16 flex-col justify-between">
          <view class="block_6 flex-col">
            <view class="group_28 flex-col"></view>
          </view>
          <text class="text-group_16">技师</text>
        </view>
        <view class="image-text_17 flex-col justify-between">
          <image
            class="label_3"
            referrerpolicy="no-referrer"
            src="/static/lanhu_shouyejishikapian/b1dc8ac20d2e485da0a997f619c96d5c_mergeImage.png"
          />
          <text class="text-group_17">订单</text>
        </view>
        <view class="image-text_18 flex-col justify-between">
          <image
            class="label_4"
            referrerpolicy="no-referrer"
            src="/static/lanhu_shouyejishikapian/3b4e0933d3db4adbb15d341775f3edc1_mergeImage.png"
          />
          <text class="text-group_18">我的</text>
        </view>
      </view>

    </view>
  </view>
  </scroll-view>
</template>
<script>
export default {
  data() {
    return {
      constants: {},
      tabsIndex: 0, // 默认显示地图视图
      layoutMode: this.getLayoutModeFromCache(),
      // 定位相关数据
      currentAddress: '重庆市渝中区名族路188号...', // 当前地址
      isLocating: false, // 定位状态
      // 选中技师相关数据
      selectedTechnician: null, // 当前选中的技师
      isMarkerClicked: false, // 标记是否刚点击了技师标记
      // 动画相关数据
      mapAnimating: false, // 地图动画状态
      // 地图相关数据
      mapCenter: {
        latitude: 29.5647, // 重庆市中心坐标
        longitude: 106.5507
      },
      mapScale: 15, // 地图缩放级别
      mapMarkers: [], // 地图标记点数据
      mapLoaded: false, // 地图加载状态
      isH5: false, // H5环境标识
      h5Map: null, // H5地图实例
      resizeTimer: null, // 窗口大小变化防抖定时器
      serviceItems: [
        { id: 1, text: '全部技师' },
        { id: 2, text: '免费出行' },
        { id: 3, text: '狐狸到家' },
        { id: 4, text: '经典按摩' },
        { id: 5, text: '深度按摩' },
        { id: 6, text: '足疗服务' },
        { id: 7, text: '推拿理疗' },
        { id: 8, text: '养生保健' }
      ],
      // 技师列表数据
      technicianList: [
        {
          id: 1,
          name: '林欣蕾',
          status: '可预约',
          availableTime: '11:00',
          earliestTime: '11:00',
          rating: 5,
          serviceCount: 489,
          freeTravel: true,
          comments: 0,
          favorites: 0,
          shopType: '商家',
          distance: '2.8km',
          lanhuBg13: 'rgba(11,206,148,1.000000)',
          avatar: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png',
          freeIcon: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG10f0b5a89ef006ddaa010e088ddc9e95.png',
          // 地图坐标（重庆市渝中区附近）
          latitude: 29.5647,
          longitude: 106.5507
        },
        {
          id: 2,
          name: '林欣蕾',
          status: '不可预约',
          availableTime: '12:00',
          earliestTime: '12:00',
          rating: 4.8,
          serviceCount: 356,
          freeTravel: false,
          comments: 5,
          favorites: 12,
          shopType: '商家',
          distance: '2.8km',
          lanhuBg13: 'rgba(153, 153, 153, 1)',
          avatar: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png',
          freeIcon: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG8ff91852365737b86422245e75027644.png',
          // 地图坐标（重庆市江北区附近）
          latitude: 29.5847,
          longitude: 106.5307
        },
        {
          id: 3,
          name: '林欣蕾',
          status: '可预约',
          availableTime: '10:30',
          earliestTime: '10:30',
          rating: 4.9,
          serviceCount: 267,
          freeTravel: true,
          comments: 8,
          favorites: 15,
          shopType: '商家',
          distance: '2.8km',
          lanhuBg13: 'rgba(11,206,148,1.000000)',
          avatar: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png',
          freeIcon: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG10f0b5a89ef006ddaa010e088ddc9e95.png',
          // 地图坐标（重庆市南岸区附近）
          latitude: 29.5447,
          longitude: 106.5707
        },
        {
          id: 4,
          name: '林欣蕾',
          status: '可预约',
          availableTime: '14:00',
          earliestTime: '14:00',
          rating: 5.0,
          serviceCount: 523,
          freeTravel: true,
          comments: 12,
          favorites: 28,
          shopType: '商家',
          distance: '2.8km',
          lanhuBg13: 'rgba(11,206,148,1.000000)',
          avatar: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png',
          freeIcon: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG10f0b5a89ef006ddaa010e088ddc9e95.png',
          // 地图坐标（重庆市沙坪坝区附近）
          latitude: 29.5747,
          longitude: 106.4707
        }
      ]
    };
  },
  mounted() {
    // 页面加载时确保布局模式正确初始化
    console.log('页面加载，当前布局模式:', this.layoutMode === 0 ? '一行两列' : '一行一列');

    // 检测运行环境
    this.checkEnvironment();

    // 初始化地图标记点
    this.initMapMarkers();

    // 如果是H5环境，初始化腾讯地图
    if (this.isH5) {
      // 添加窗口大小变化监听器
      // #ifdef H5
      window.addEventListener('resize', this.handleWindowResize);
      window.addEventListener('orientationchange', this.handleWindowResize);
      // #endif

      this.$nextTick(() => {
        this.initH5Map();
      });
    }
  },

  beforeDestroy() {
    // 清理事件监听器
    // #ifdef H5
    if (this.isH5) {
      window.removeEventListener('resize', this.handleWindowResize);
      window.removeEventListener('orientationchange', this.handleWindowResize);
    }
    // #endif
  },

  methods: {
    // 检测运行环境
    checkEnvironment() {
      // #ifdef H5
      this.isH5 = true;
      console.log('当前运行在H5环境');
      // #endif

      // #ifndef H5
      this.isH5 = false;
      console.log('当前运行在非H5环境');
      // #endif
    },

    // 初始化H5地图
    initH5Map() {
      if (!this.isH5) return;

      console.log('开始初始化H5 Leaflet地图...');

      // 动态加载Leaflet库
      this.loadLeafletLibrary().then(() => {
        console.log('Leaflet库加载成功，开始创建地图');
        this.createLeafletMap();
      }).catch(err => {
        console.error('加载Leaflet库失败:', err);
        this.mapLoaded = true;
      });
    },

    // 动态加载Leaflet库
    loadLeafletLibrary() {
      return new Promise((resolve, reject) => {
        // 检查是否已经加载
        if (window.L) {
          console.log('Leaflet库已存在');
          resolve();
          return;
        }

        console.log('开始加载Leaflet CSS和JS...');

        // 加载CSS
        const cssLink = document.createElement('link');
        cssLink.rel = 'stylesheet';
        cssLink.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
        document.head.appendChild(cssLink);

        // 加载JS
        const script = document.createElement('script');
        script.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js';
        script.onload = () => {
          console.log('Leaflet库加载完成');
          resolve();
        };
        script.onerror = (error) => {
          console.error('Leaflet库加载失败:', error);
          reject(error);
        };
        document.head.appendChild(script);
      });
    },

    // 创建Leaflet地图
    createLeafletMap() {
      try {
        console.log('开始创建Leaflet地图实例...');
        const mapContainer = document.getElementById('leafletMapContainer');
        if (!mapContainer) {
          console.error('地图容器未找到');
          this.mapLoaded = true;
          return;
        }

        console.log('地图容器找到，创建Leaflet地图实例');

        // 创建地图实例
        this.h5Map = L.map('leafletMapContainer', {
          attributionControl: false // 禁用版权信息控件
        }).setView(
          [this.mapCenter.latitude, this.mapCenter.longitude],
          this.mapScale
        );

        // 添加地图瓦片层（免费，无需API密钥）
        // 优先使用OpenStreetMap，如果加载失败则使用备用服务
        const tileLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution: '', // 移除版权信息
          maxZoom: 19,
          errorTileUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
        });

        // 添加瓦片层到地图
        tileLayer.addTo(this.h5Map);

        // 监听瓦片加载错误，如果主服务不可用则切换到备用服务
        tileLayer.on('tileerror', () => {
          console.log('主地图服务加载失败，尝试备用服务...');
          // 移除当前瓦片层
          this.h5Map.removeLayer(tileLayer);
          // 添加备用瓦片层
          L.tileLayer('https://cartodb-basemaps-{s}.global.ssl.fastly.net/light_all/{z}/{x}/{y}.png', {
            attribution: '', // 移除版权信息
            maxZoom: 19,
            subdomains: 'abcd'
          }).addTo(this.h5Map);
        });

        console.log('地图实例创建成功');

        // 监听地图加载完成事件
        this.h5Map.whenReady(() => {
          console.log('地图瓦片加载完成，开始添加技师标记');
          // 添加技师标记
          this.addLeafletMarkers();
          this.mapLoaded = true;
          console.log('H5 Leaflet地图完全初始化完成');
        });

        // 监听地图视图变化
        this.h5Map.on('moveend', () => {
          console.log('地图移动完成，当前中心点:', this.h5Map.getCenter());
        });

        this.h5Map.on('zoomend', () => {
          console.log('地图缩放完成，当前缩放级别:', this.h5Map.getZoom());
        });

        // 监听地图点击事件（用于关闭技师卡片）
        this.h5Map.on('click', () => {
          // 如果刚点击了标记，则不关闭卡片
          if (this.isMarkerClicked) {
            return;
          }
          // 点击地图空白区域时关闭技师卡片
          if (this.selectedTechnician) {
            this.closeSelectedTechnician();
          }
        });

        // 设置地图容器高度以铺满剩余空间
        this.setMapContainerHeight();
      } catch (error) {
        console.error('创建Leaflet地图失败:', error);
        this.mapLoaded = true;
      }
    },

    // 添加Leaflet地图标记
    addLeafletMarkers() {
      if (!this.h5Map) {
        console.log('地图实例不存在，无法添加标记');
        return;
      }

      console.log('开始添加技师标记...');

      this.technicianList.forEach((technician) => {
        if (technician.latitude && technician.longitude) {
          // 创建自定义图标
          const customIcon = L.divIcon({
            className: 'custom-marker',
            html: `
              <div class="marker-container" style="display: flex; align-items: center; background: rgba(255, 255, 255, 0.95); border-radius: 6px; padding: 2px 4px; box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15); cursor: pointer; max-width: 100px;">
                <img src="${technician.avatar}" class="marker-avatar" style="width: 24px !important; height: 24px !important; border-radius: 50% !important; margin-right: 4px; object-fit: cover !important; border: 1px solid #fff !important;" />
                <div class="marker-info" style="display: flex; flex-direction: column;">
                  <div class="marker-name" style="font-size: 11px; font-weight: 500; color: #333; line-height: 1.2; margin-bottom: 1px;">${technician.name}</div>
                  <div class="marker-distance" style="font-size: 9px; color: #0BCE94; font-weight: 600; line-height: 1;">${technician.distance}</div>
                </div>
              </div>
            `,
            iconSize: [100, 40], // 从[120, 60]调小到[100, 40]
            iconAnchor: [50, 20] // 相应调整锚点位置
          });

          // 添加标记到地图
          const marker = L.marker([technician.latitude, technician.longitude], {
            icon: customIcon
          }).addTo(this.h5Map);

          // 添加点击事件
          marker.on('click', () => {
            this.onTechnicianClick(technician);
          });

          console.log(`添加技师标记: ${technician.name}`);
        }
      });
    },

    // 设置地图容器高度以铺满剩余空间
    setMapContainerHeight() {
      if (!this.isH5) return;

      this.$nextTick(() => {
        try {
          // 获取视口高度
          const viewportHeight = window.innerHeight;

          // 获取页面头部区域的高度
          const headerElements = document.querySelectorAll('.section_1, .section_2 .group_1');
          let headerHeight = 0;
          headerElements.forEach(el => {
            headerHeight += el.offsetHeight;
          });

          // 计算地图应该占用的高度（视口高度 - 头部高度 - 底部导航栏高度 - 边距）
          const bottomNavHeight = 60; // 底部导航栏高度
          const margins = 40; // 上下边距
          const availableHeight = viewportHeight - headerHeight - bottomNavHeight - margins;

          // 设置最小和最大高度限制
          const minHeight = 300;
          const maxHeight = viewportHeight * 0.7;
          const finalHeight = Math.max(minHeight, Math.min(availableHeight, maxHeight));

          console.log('计算地图高度:', {
            viewportHeight,
            headerHeight,
            availableHeight,
            finalHeight
          });

          // 应用高度到地图容器
          const mapContainer = document.querySelector('.map-container') || document.querySelector('.map-container.h5-fullscreen');
          const leafletContainer = document.getElementById('leafletMapContainer');

          console.log('设置地图容器高度:', {
            mapContainer: mapContainer ? '找到' : '未找到',
            leafletContainer: leafletContainer ? '找到' : '未找到',
            finalHeight: finalHeight + 'px'
          });

          if (mapContainer) {
            mapContainer.style.height = finalHeight + 'px';
            mapContainer.style.minHeight = finalHeight + 'px';
            console.log('地图容器高度已设置为:', finalHeight + 'px');
          }

          if (leafletContainer) {
            leafletContainer.style.height = finalHeight + 'px';
            leafletContainer.style.minHeight = finalHeight + 'px';
            console.log('Leaflet容器高度已设置为:', finalHeight + 'px');
          }

          // 触发地图重新计算大小
          if (this.h5Map) {
            setTimeout(() => {
              this.h5Map.invalidateSize();
              console.log('地图尺寸已重新计算');
            }, 100);
          }
        } catch (error) {
          console.error('设置地图高度失败:', error);
        }
      });
    },

    // 处理窗口大小变化
    handleWindowResize() {
      if (!this.isH5) return;

      // 防抖处理，避免频繁调用
      clearTimeout(this.resizeTimer);
      this.resizeTimer = setTimeout(() => {
        console.log('窗口大小变化，重新计算地图高度');
        this.setMapContainerHeight();
      }, 300);
    },

    // 技师点击事件（H5地图标记点击）
    onTechnicianClick(technician) {
      console.log('点击技师:', technician.name);
      this.selectedTechnician = technician;
      // 设置标记，防止地图点击事件立即关闭卡片
      this.isMarkerClicked = true;
      setTimeout(() => {
        this.isMarkerClicked = false;
      }, 100);
      uni.showToast({
        title: `选择了${technician.name}`,
        icon: 'none'
      });
    },

    // 从缓存中读取布局模式
    getLayoutModeFromCache() {
      try {
        const cachedLayoutMode = uni.getStorageSync('layoutMode');
        if (cachedLayoutMode !== '') {
          console.log('从缓存读取布局模式:', cachedLayoutMode === 0 ? '一行两列' : '一行一列');
          return cachedLayoutMode;
        }
      } catch (e) {
        console.log('读取缓存失败:', e);
      }
      // 默认返回一行一列布局
      return 1;
    },

    // 保存布局模式到缓存
    saveLayoutModeToCache(layoutMode) {
      try {
        uni.setStorageSync('layoutMode', layoutMode);
        console.log('布局模式已保存到缓存:', layoutMode === 0 ? '一行两列' : '一行一列');
      } catch (e) {
        console.log('保存缓存失败:', e);
      }
    },

    // 切换tabs
    switchTab(index) {
      if (index === 0) {
        // 点击地图tab
        this.tabsIndex = 0;
        console.log('切换到tab: 地图');

        // 切换到地图时，触发动画并刷新地图
        this.mapAnimating = true;

        this.$nextTick(() => {
          // 等待DOM显示状态切换完成
          setTimeout(() => {
            console.log('切换到地图视图，刷新地图显示');

            if (this.isH5 && this.h5Map) {
              // H5环境：地图实例已存在，只需要重新计算尺寸
              console.log('H5地图实例存在，重新计算尺寸');
              this.h5Map.invalidateSize();
            } else if (this.isH5 && !this.h5Map) {
              // H5环境：地图实例不存在，需要初始化
              console.log('H5地图实例不存在，初始化地图');
              this.initH5Map();
            } else {
              // 小程序环境：触发地图组件刷新
              console.log('小程序环境，触发地图刷新');
              this.mapLoaded = false;
              this.$nextTick(() => {
                this.mapLoaded = true;
              });
            }

            // 动画完成后重置状态
            setTimeout(() => {
              this.mapAnimating = false;
            }, 500);
          }, 100); // 给足够时间让v-show完成显示切换
        });
      } else if (index === 1) {
        // 点击列表tab
        if (this.tabsIndex === 1) {
          // 如果当前已经是列表tab，则切换布局模式
          this.layoutMode = this.layoutMode === 0 ? 1 : 0;
          // 保存新的布局模式到缓存
          this.saveLayoutModeToCache(this.layoutMode);
          console.log('切换列表布局模式:', this.layoutMode === 0 ? '一行两列' : '一行一列');
        } else {
          // 如果当前不是列表tab，则切换到列表tab
          this.tabsIndex = 1;
          console.log('切换到tab: 列表');
          // 切换到列表时，关闭选中的技师卡片
          if (this.selectedTechnician) {
            this.closeSelectedTechnician();
          }
        }
      }
    },

    // 初始化地图标记点
    initMapMarkers() {
      console.log('开始初始化地图标记点...');

      // 基于技师列表生成地图标记点
      this.mapMarkers = this.technicianList.map((technician) => {
        // 为每个技师分配不同的坐标，避免重叠
        const offsetLat = (Math.random() - 0.5) * 0.01; // 随机偏移
        const offsetLng = (Math.random() - 0.5) * 0.01;

        return {
          id: technician.id,
          latitude: technician.latitude + offsetLat,
          longitude: technician.longitude + offsetLng,
          title: technician.name,
          iconPath: '', // H5环境下使用默认标记
          width: 30,
          height: 30,
          callout: {
            content: `${technician.name} ${technician.distance}`,
            color: '#ffffff',
            fontSize: 14,
            borderRadius: 8,
            bgColor: '#0BCE94',
            padding: 10,
            display: 'ALWAYS'
          }
        };
      });

      console.log('地图标记点初始化完成:', this.mapMarkers);

      // 延迟设置地图加载完成状态
      setTimeout(() => {
        this.mapLoaded = true;
        console.log('地图加载完成');
      }, 1000);
    },

    // 地图标记点点击事件（uni-app原生地图）
    onMarkerTap(e) {
      const markerId = e.detail.markerId;
      const technician = this.technicianList.find(item => item.id === markerId);
      if (technician) {
        console.log('点击了技师标记:', technician.name);
        this.selectedTechnician = technician;
        // 设置标记，防止地图点击事件立即关闭卡片
        this.isMarkerClicked = true;
        setTimeout(() => {
          this.isMarkerClicked = false;
        }, 100);
        uni.showToast({
          title: `选择了技师: ${technician.name}`,
          icon: 'none'
        });
      }
    },

    // 地图区域变化事件
    onRegionChange(e) {
      console.log('地图区域变化:', e.detail);
    },

    // 地图控制方法
    centerToUserLocation() {
      // 获取用户当前位置并居中显示
      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          this.mapCenter = {
            latitude: res.latitude,
            longitude: res.longitude
          };
          console.log('定位到用户位置:', this.mapCenter);
        },
        fail: (err) => {
          console.error('获取位置失败:', err);
          uni.showToast({
            title: '定位失败',
            icon: 'none'
          });
        }
      });
    },

    zoomIn() {
      if (this.mapScale < 18) {
        this.mapScale += 1;
        console.log('地图放大，当前缩放级别:', this.mapScale);
      }
    },

    zoomOut() {
      if (this.mapScale > 5) {
        this.mapScale -= 1;
        console.log('地图缩小，当前缩放级别:', this.mapScale);
      }
    },

    // 获取自定义标记样式（预留，当前使用原生标记）
    getMarkerStyle() {
      return {
        display: 'none' // 暂时隐藏自定义标记，使用原生标记
      };
    },

    // 处理定位图片点击事件
    async handleLocationClick() {
      if (this.isLocating) {
        return; // 防止重复点击
      }

      this.isLocating = true;

      try {
        // 显示加载提示
        uni.showLoading({
          title: '正在定位...',
          mask: true
        });

        // 检测运行环境
        const platform = uni.getSystemInfoSync().platform;
        const isH5 = platform === 'h5' || typeof window !== 'undefined';

        let location;

        if (isH5) {
          // H5环境使用浏览器定位API
          location = await this.getH5Location();
        } else {
          // 小程序环境使用uni.getLocation
          location = await this.getUniLocation();
        }

        if (location) {
          // 更新地图中心点
          this.mapCenter = {
            latitude: location.latitude,
            longitude: location.longitude
          };

          // 根据经纬度获取地址信息（这里使用模拟地址，实际项目中可以调用逆地理编码API）
          await this.getAddressByLocation(location.latitude, location.longitude);

          // 显示定位成功提示
          uni.showToast({
            title: '定位成功',
            icon: 'success'
          });

          // 如果是地图视图，重新初始化标记点
          if (this.tabsIndex === 0) {
            this.initMapMarkers();
            if (this.isH5 && this.h5Map) {
              this.h5Map.setView([location.latitude, location.longitude], this.mapScale);
            }
          }
        }

      } catch (error) {
        console.error('定位失败:', error);
        this.handleLocationError(error);
      } finally {
        this.isLocating = false;
        uni.hideLoading();
      }
    },

    // H5环境定位
    getH5Location() {
      return new Promise((resolve, reject) => {
        if (!navigator.geolocation) {
          reject(new Error('浏览器不支持定位功能'));
          return;
        }

        navigator.geolocation.getCurrentPosition(
          (position) => {
            resolve({
              latitude: position.coords.latitude,
              longitude: position.coords.longitude
            });
          },
          (error) => {
            let message = '定位失败';
            switch (error.code) {
              case error.PERMISSION_DENIED:
                message = '用户拒绝了定位请求';
                break;
              case error.POSITION_UNAVAILABLE:
                message = '位置信息不可用';
                break;
              case error.TIMEOUT:
                message = '定位请求超时';
                break;
            }
            reject(new Error(message));
          },
          {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 60000
          }
        );
      });
    },

    // 小程序环境定位
    getUniLocation() {
      return new Promise((resolve, reject) => {
        uni.getLocation({
          type: 'gcj02', // 使用国测局坐标系
          success: (res) => {
            resolve({
              latitude: res.latitude,
              longitude: res.longitude
            });
          },
          fail: (error) => {
            let message = '定位失败';
            if (error.errMsg) {
              if (error.errMsg.includes('auth deny')) {
                message = '用户拒绝了定位权限';
              } else if (error.errMsg.includes('timeout')) {
                message = '定位超时，请重试';
              } else if (error.errMsg.includes('system deny')) {
                message = '系统拒绝定位，请检查设置';
              }
            }
            reject(new Error(message));
          }
        });
      });
    },

    // 根据经纬度获取地址信息
    async getAddressByLocation(latitude, longitude) {
      try {
        // 这里使用模拟地址，实际项目中可以调用腾讯地图或高德地图的逆地理编码API
        // 示例API调用（需要配置API密钥）：
        // const response = await uni.request({
        //   url: `https://apis.map.qq.com/ws/geocoder/v1/?location=${latitude},${longitude}&key=YOUR_API_KEY`,
        //   method: 'GET'
        // });

        // 模拟根据坐标生成地址
        const mockAddresses = [
          '重庆市渝中区解放碑步行街88号',
          '重庆市江北区观音桥步行街168号',
          '重庆市南岸区南坪步行街288号',
          '重庆市沙坪坝区三峡广场128号'
        ];

        // 根据坐标选择一个模拟地址
        const index = Math.floor((latitude + longitude) * 100) % mockAddresses.length;
        this.currentAddress = mockAddresses[index];

        console.log('获取到地址:', this.currentAddress);
      } catch (error) {
        console.error('获取地址失败:', error);
        // 如果获取地址失败，使用默认地址
        this.currentAddress = `定位成功 (${latitude.toFixed(4)}, ${longitude.toFixed(4)})`;
      }
    },

    // 处理定位错误
    handleLocationError(error) {
      let message = '定位失败';

      if (error.message) {
        message = error.message;
      }

      uni.showToast({
        title: message,
        icon: 'none',
        duration: 3000
      });

      console.error('定位错误详情:', error);
    },

    // 关闭选中技师卡片
    closeSelectedTechnician() {
      this.selectedTechnician = null;
    },

    // 地图点击事件（用于关闭技师卡片）
    onMapClick() {
      // 如果刚点击了标记，则不关闭卡片
      if (this.isMarkerClicked) {
        return;
      }
      // 点击地图空白区域时关闭技师卡片
      if (this.selectedTechnician) {
        this.closeSelectedTechnician();
      }
    },

    // 刷新地图显示（仅在特殊情况下使用，如窗口大小变化）
    refreshMapDisplay() {
      console.log('刷新地图显示...');

      if (this.isH5 && this.h5Map) {
        // H5环境：重新计算地图尺寸
        console.log('重新计算H5地图尺寸');
        this.setMapContainerHeight();
        setTimeout(() => {
          this.h5Map.invalidateSize();
          console.log('H5地图尺寸已重新计算');
        }, 100);
      } else if (this.isH5 && !this.h5Map) {
        // H5环境：地图实例不存在，重新初始化
        console.log('H5地图实例不存在，重新初始化');
        this.initH5Map();
      } else {
        // 小程序环境：触发地图组件重新渲染
        console.log('刷新小程序地图');
        this.mapLoaded = false;
        this.$nextTick(() => {
          this.mapLoaded = true;
          console.log('小程序地图已刷新');
        });
      }
    }
  }
};
</script>
<style lang='scss'>
@import '../common/common.scss';
@import './assets/style/index.rpx.scss';
</style>
