.page {
  background-color: rgba(248, 248, 248, 1);
  position: relative;
  width: 375px;
  height: 812px;
  overflow: hidden;
  .section_1 {
    background-color: rgba(0, 0, 0, 1);
    width: 375px;
    height: 86px;
    .box_1 {
      background-color: rgba(255, 255, 255, 1);
      width: 375px;
      height: 32px;
      .text_1 {
        width: 32px;
        height: 18px;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 12px;
        font-family: Inter-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 150px;
        margin: 7px 0 0 16px;
      }
      .thumbnail_1 {
        width: 18px;
        height: 18px;
        margin: 7px 0 0 251px;
      }
      .thumbnail_2 {
        width: 18px;
        height: 18px;
        margin: 7px 0 0 3px;
      }
      .thumbnail_3 {
        width: 19px;
        height: 19px;
        margin: 7px 15px 0 3px;
      }
    }
    .box_2 {
      background-color: rgba(255, 255, 255, 1);
      width: 375px;
      height: 54px;
      .text_2 {
        width: 64px;
        height: 22px;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 16px;
        font-family: Inter-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 22px;
        margin: 16px 0 0 12px;
      }
      .image_1 {
        width: 87px;
        height: 32px;
        margin: 11px 12px 0 0;
      }
    }
  }
  .section_2 {
    position: relative;
    width: 375px;
    height: 727px;
    margin-bottom: 1px;
    .group_1 {
      background-color: rgba(11, 206, 148, 1);
      width: 375px;
      height: 162px;
      .box_3 {
        width: 350px;
        height: 23px;
        margin: 15px 0 0 13px;
        .thumbnail_4 {
          width: 16px;
          height: 17px;
          margin-top: 1px;
        }
        .image-text_1 {
          width: 211px;
          height: 15px;
          margin: 2px 0 0 11px;
          .text-group_1 {
            width: 186px;
            height: 15px;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 15px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 100px;
          }
          .box_4 {
            background-color: rgba(255, 255, 255, 1);
            width: 16px;
            height: 10px;
            margin-top: 2px;
          }
        }
        .block_1 {
          background-color: rgba(255, 255, 255, 0.2);
          border-radius: 2px 0px 2px 0px;
          width: 46px;
          height: 23px;
          margin-left: 20px;
          .image-text_2 {
            width: 32px;
            height: 9px;
            margin: 7px 0 0 8px;
            .thumbnail_5 {
              width: 9px;
              height: 9px;
            }
            .text-group_2 {
              width: 20px;
              height: 9px;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 10px;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 26px;
            }
          }
        }
        .block_2 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 0px 2px 0px 2px;
          width: 46px;
          height: 23px;
          border: 1px solid rgba(224, 224, 224, 1);
          .image-text_3 {
            width: 31px;
            height: 9px;
            margin: 7px 0 0 9px;
            .thumbnail_6 {
              width: 9px;
              height: 9px;
            }
            .text-group_3 {
              width: 20px;
              height: 9px;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 10px;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 26px;
            }
          }
        }
      }
      .box_5 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 50px;
        width: 351px;
        height: 38px;
        margin: 11px 0 0 12px;
        .image-text_4 {
          width: 70px;
          height: 13px;
          margin: 13px 0 0 17px;
          .text-group_4 {
            width: 52px;
            height: 13px;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 13px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 100px;
          }
          .group_2 {
            background-color: rgba(2, 2, 2, 1);
            width: 10px;
            height: 6px;
            margin-top: 3px;
          }
        }
        .text_3 {
          width: 117px;
          height: 13px;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 13px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin: 13px 0 0 27px;
        }
        .label_1 {
          width: 24px;
          height: 24px;
          margin: 7px 11px 0 85px;
        }
      }
      .box_6 {
        width: 304px;
        height: 23px;
        margin: 14px 0 0 15px;
        .text-wrapper_1 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 100px;
          height: 23px;
          width: 70px;
          .text_4 {
            width: 52px;
            height: 19px;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 13px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 13px;
            margin: 2px 0 0 9px;
          }
        }
        .text-wrapper_2 {
          background-color: rgba(34, 34, 34, 0.1);
          border-radius: 100px;
          height: 23px;
          margin-left: 8px;
          width: 70px;
          .text_5 {
            width: 52px;
            height: 19px;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 13px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 13px;
            margin: 2px 0 0 9px;
          }
        }
        .text-wrapper_3 {
          background-color: rgba(34, 34, 34, 0.1);
          border-radius: 100px;
          height: 23px;
          margin-left: 8px;
          width: 70px;
          .text_6 {
            width: 52px;
            height: 19px;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 13px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 13px;
            margin: 2px 0 0 9px;
          }
        }
        .text-wrapper_4 {
          background-color: rgba(34, 34, 34, 0.1);
          border-radius: 100px;
          height: 23px;
          margin-left: 8px;
          width: 70px;
          .text_7 {
            width: 52px;
            height: 19px;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 13px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 13px;
            margin: 2px 0 0 9px;
          }
        }
      }
      .box_7 {
        width: 350px;
        height: 18px;
        margin: 11px 0 9px 17px;
        .text_8 {
          width: 48px;
          height: 18px;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 12px;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 12px;
        }
        .group_3 {
          background-color: rgba(255, 255, 255, 1);
          width: 9px;
          height: 6px;
          margin: 12px 0 0 11px;
        }
        .text_9 {
          width: 48px;
          height: 18px;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 12px;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 12px;
          margin-left: 26px;
        }
        .group_4 {
          background-color: rgba(255, 255, 255, 1);
          width: 9px;
          height: 6px;
          margin: 12px 0 0 11px;
        }
        .text_10 {
          width: 48px;
          height: 18px;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 12px;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 12px;
          margin-left: 26px;
        }
        .group_5 {
          background-color: rgba(255, 255, 255, 1);
          width: 9px;
          height: 6px;
          margin: 12px 0 0 11px;
        }
        .text_11 {
          width: 48px;
          height: 18px;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 12px;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 12px;
          margin-left: 26px;
        }
        .group_6 {
          background-color: rgba(255, 255, 255, 1);
          width: 9px;
          height: 6px;
          margin: 12px 0 0 11px;
        }
      }
    }
    .group_7 {
      width: 351px;
      height: 168px;
      margin: 10px 0 0 12px;
      .group_8 {
        height: 168px;
        background: url(/static/lanhu_shouyejishikapian/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png)
          100% no-repeat;
        background-size: 100% 100%;
        width: 170px;
        .group_9 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 20px;
          width: 111px;
          height: 21px;
          margin: 136px 0 0 25px;
          .text-wrapper_5 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 20px;
            height: 21px;
            width: 61px;
            .text_12 {
              width: 49px;
              height: 12px;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 12px;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 100px;
              margin: 4px 0 0 4px;
            }
          }
          .text_13 {
            width: 31px;
            height: 12px;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 12px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 100px;
            margin: 4px 13px 0 6px;
          }
        }
      }
      .group_10 {
        height: 168px;
        background: url(/static/lanhu_shouyejishikapian/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png)
          100% no-repeat;
        background-size: 100% 100%;
        width: 170px;
        .box_8 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 20px;
          width: 111px;
          height: 21px;
          margin: 136px 0 0 25px;
          .text-wrapper_6 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 20px;
            height: 21px;
            width: 61px;
            .text_14 {
              width: 49px;
              height: 12px;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 12px;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 100px;
              margin: 4px 0 0 4px;
            }
          }
          .text_15 {
            width: 31px;
            height: 12px;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 12px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 100px;
            margin: 4px 13px 0 6px;
          }
        }
      }
    }
    .group_11 {
      width: 351px;
      height: 143px;
      margin-left: 12px;
      .box_9 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 0px 0px 5px 5px;
        width: 170px;
        height: 143px;
        .box_10 {
          width: 101px;
          height: 17px;
          margin: 10px 0 0 11px;
          .text_16 {
            width: 46px;
            height: 15px;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 15px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 100px;
          }
          .text-wrapper_7 {
            background-color: rgba(30, 30, 30, 1);
            border-radius: 50px;
            height: 16px;
            margin-top: 1px;
            width: 47px;
            .text_17 {
              width: 33px;
              height: 8px;
              overflow-wrap: break-word;
              color: rgba(234, 225, 196, 1);
              font-size: 10px;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 100px;
              margin: 3px 0 0 7px;
            }
          }
        }
        .image-text_5 {
          width: 97px;
          height: 12px;
          margin: 8px 0 0 11px;
          .group_12 {
            width: 12px;
            height: 11px;
            background: url(/static/lanhu_shouyejishikapian/FigmaDDSSlicePNG751b001b58a95440d7eae2a8c0959d9d.png)
              100% no-repeat;
            background-size: 100% 100%;
            margin-top: 1px;
          }
          .text-group_5 {
            width: 82px;
            height: 12px;
            .text_18 {
              width: 8px;
              height: 12px;
              overflow-wrap: break-word;
              color: rgba(247, 160, 68, 1);
              font-size: 12px;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 100px;
            }
            .text_19 {
              width: 69px;
              height: 12px;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 12px;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 100px;
            }
          }
        }
        .image-text_6 {
          width: 73px;
          height: 12px;
          margin: 8px 0 0 12px;
          .thumbnail_7 {
            width: 9px;
            height: 9px;
            margin-top: 2px;
          }
          .text-group_6 {
            width: 61px;
            height: 12px;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 12px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 100px;
          }
        }
        .box_11 {
          width: 146px;
          height: 27px;
          margin: 11px 0 0 13px;
          .text-wrapper_8 {
            border-radius: 4px;
            height: 27px;
            border: 1px solid rgba(11, 206, 148, 1);
            width: 67px;
            .text_20 {
              width: 51px;
              height: 15px;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 12px;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 100px;
              margin: 7px 0 0 8px;
            }
          }
          .text-wrapper_9 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 5px;
            height: 27px;
            width: 67px;
            .text_21 {
              width: 49px;
              height: 12px;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 12px;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 100px;
              margin: 7px 0 0 11px;
            }
          }
        }
        .box_12 {
          width: 146px;
          height: 14px;
          margin: 17px 0 7px 13px;
          .group_13 {
            background-color: rgba(150, 150, 150, 1);
            width: 16px;
            height: 14px;
          }
          .text_22 {
            width: 8px;
            height: 12px;
            overflow-wrap: break-word;
            color: rgba(150, 150, 150, 1);
            font-size: 12px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 100px;
            margin: 1px 0 0 3px;
          }
          .thumbnail_8 {
            width: 15px;
            height: 14px;
            margin-left: 25px;
          }
          .text_23 {
            width: 8px;
            height: 12px;
            overflow-wrap: break-word;
            color: rgba(150, 150, 150, 1);
            font-size: 12px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 100px;
            margin: 1px 0 0 3px;
          }
          .image-text_7 {
            width: 42px;
            height: 12px;
            margin: 1px 0 0 26px;
            .group_14 {
              background-color: rgba(150, 150, 150, 1);
              width: 13px;
              height: 12px;
            }
            .text-group_7 {
              width: 25px;
              height: 12px;
              overflow-wrap: break-word;
              color: rgba(150, 150, 150, 1);
              font-size: 12px;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 100px;
            }
          }
        }
      }
      .box_13 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 0px 0px 5px 5px;
        width: 170px;
        height: 143px;
        .group_15 {
          width: 101px;
          height: 17px;
          margin: 10px 0 0 11px;
          .text_24 {
            width: 46px;
            height: 15px;
            overflow-wrap: break-word;
            color: rgba(0, 0, 0, 1);
            font-size: 15px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 100px;
          }
          .text-wrapper_10 {
            background-color: rgba(30, 30, 30, 1);
            border-radius: 50px;
            height: 16px;
            margin-top: 1px;
            width: 47px;
            .text_25 {
              width: 33px;
              height: 8px;
              overflow-wrap: break-word;
              color: rgba(234, 225, 196, 1);
              font-size: 10px;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 100px;
              margin: 3px 0 0 7px;
            }
          }
        }
        .image-text_8 {
          width: 97px;
          height: 12px;
          margin: 8px 0 0 11px;
          .section_3 {
            width: 12px;
            height: 11px;
            background: url(/static/lanhu_shouyejishikapian/FigmaDDSSlicePNG751b001b58a95440d7eae2a8c0959d9d.png)
              100% no-repeat;
            background-size: 100% 100%;
            margin-top: 1px;
          }
          .text-group_8 {
            width: 82px;
            height: 12px;
            .text_26 {
              width: 8px;
              height: 12px;
              overflow-wrap: break-word;
              color: rgba(247, 160, 68, 1);
              font-size: 12px;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 100px;
            }
            .text_27 {
              width: 69px;
              height: 12px;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 12px;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 100px;
            }
          }
        }
        .image-text_9 {
          width: 73px;
          height: 12px;
          margin: 8px 0 0 12px;
          .thumbnail_9 {
            width: 9px;
            height: 9px;
            margin-top: 2px;
          }
          .text-group_9 {
            width: 61px;
            height: 12px;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 12px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 100px;
          }
        }
        .group_16 {
          width: 146px;
          height: 27px;
          margin: 11px 0 0 13px;
          .text-wrapper_11 {
            border-radius: 4px;
            height: 27px;
            border: 1px solid rgba(11, 206, 148, 1);
            width: 67px;
            .text_28 {
              width: 51px;
              height: 15px;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 12px;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 100px;
              margin: 7px 0 0 8px;
            }
          }
          .text-wrapper_12 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 5px;
            height: 27px;
            width: 67px;
            .text_29 {
              width: 49px;
              height: 12px;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 12px;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 100px;
              margin: 7px 0 0 11px;
            }
          }
        }
        .group_17 {
          width: 146px;
          height: 14px;
          margin: 17px 0 7px 13px;
          .box_14 {
            background-color: rgba(150, 150, 150, 1);
            width: 16px;
            height: 14px;
          }
          .text_30 {
            width: 8px;
            height: 12px;
            overflow-wrap: break-word;
            color: rgba(150, 150, 150, 1);
            font-size: 12px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 100px;
            margin: 1px 0 0 3px;
          }
          .box_15 {
            background-color: rgba(150, 150, 150, 1);
            width: 15px;
            height: 14px;
            margin-left: 25px;
          }
          .text_31 {
            width: 8px;
            height: 12px;
            overflow-wrap: break-word;
            color: rgba(150, 150, 150, 1);
            font-size: 12px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 100px;
            margin: 1px 0 0 3px;
          }
          .image-text_10 {
            width: 42px;
            height: 12px;
            margin: 1px 0 0 26px;
            .section_4 {
              background-color: rgba(150, 150, 150, 1);
              width: 13px;
              height: 12px;
            }
            .text-group_10 {
              width: 25px;
              height: 12px;
              overflow-wrap: break-word;
              color: rgba(150, 150, 150, 1);
              font-size: 12px;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 100px;
            }
          }
        }
      }
    }
    .group_18 {
      width: 351px;
      height: 168px;
      margin: 10px 0 0 12px;
      .group_19 {
        width: 170px;
        height: 168px;
        background: url(/static/lanhu_shouyejishikapian/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png)
          100% no-repeat;
        background-size: 100% 100%;
        .text-wrapper_13 {
          background-color: rgba(0, 0, 0, 0.5);
          border-radius: 5px;
          height: 34px;
          width: 94px;
          margin: 34px 0 0 56px;
          .text_32 {
            width: 56px;
            height: 14px;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 14px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 100px;
            margin: 10px 0 0 19px;
          }
        }
        .block_3 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 20px;
          width: 111px;
          height: 21px;
          margin: 68px 0 11px 25px;
          .text-wrapper_14 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 20px;
            height: 21px;
            width: 61px;
            .text_33 {
              width: 49px;
              height: 12px;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 12px;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 100px;
              margin: 4px 0 0 4px;
            }
          }
          .text_34 {
            width: 31px;
            height: 12px;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 12px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 100px;
            margin: 4px 13px 0 6px;
          }
        }
      }
      .group_20 {
        height: 168px;
        background: url(/static/lanhu_shouyejishikapian/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png)
          100% no-repeat;
        background-size: 100% 100%;
        width: 170px;
        .box_16 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 20px;
          width: 111px;
          height: 21px;
          margin: 136px 0 0 25px;
          .text-wrapper_15 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 20px;
            height: 21px;
            width: 61px;
            .text_35 {
              width: 49px;
              height: 12px;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 12px;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 100px;
              margin: 4px 0 0 4px;
            }
          }
          .text_36 {
            width: 31px;
            height: 12px;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 12px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 100px;
            margin: 4px 13px 0 6px;
          }
        }
      }
    }
    .text-wrapper_16 {
      width: 118px;
      height: 12px;
      margin: 341px 0 287px 222px;
      .text_37 {
        width: 24px;
        height: 12px;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 12px;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
      }
      .text_38 {
        width: 24px;
        height: 12px;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 12px;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
      }
    }
    .group_21 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 0px 0px 5px 5px;
      position: absolute;
      left: 12px;
      top: 661px;
      width: 170px;
      height: 143px;
      .box_17 {
        width: 101px;
        height: 17px;
        margin: 10px 0 0 11px;
        .text_39 {
          width: 46px;
          height: 15px;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 15px;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 100px;
        }
        .text-wrapper_17 {
          background-color: rgba(30, 30, 30, 1);
          border-radius: 50px;
          height: 16px;
          margin-top: 1px;
          width: 47px;
          .text_40 {
            width: 33px;
            height: 8px;
            overflow-wrap: break-word;
            color: rgba(234, 225, 196, 1);
            font-size: 10px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 100px;
            margin: 3px 0 0 7px;
          }
        }
      }
      .image-text_11 {
        width: 73px;
        height: 12px;
        margin: 28px 0 0 12px;
        .thumbnail_10 {
          width: 9px;
          height: 9px;
          margin-top: 2px;
        }
        .text-group_11 {
          width: 61px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 12px;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 100px;
        }
      }
      .box_18 {
        width: 146px;
        height: 27px;
        margin: 11px 0 0 13px;
        .text-wrapper_18 {
          border-radius: 4px;
          height: 27px;
          border: 1px solid rgba(11, 206, 148, 1);
          width: 67px;
          .text_41 {
            width: 51px;
            height: 15px;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 12px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 100px;
            margin: 7px 0 0 8px;
          }
        }
        .text-wrapper_19 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 5px;
          height: 27px;
          width: 67px;
          .text_42 {
            width: 49px;
            height: 12px;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 12px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 100px;
            margin: 7px 0 0 11px;
          }
        }
      }
      .box_19 {
        width: 146px;
        height: 14px;
        margin: 17px 0 7px 13px;
        .block_4 {
          background-color: rgba(150, 150, 150, 1);
          width: 16px;
          height: 14px;
        }
        .text_43 {
          width: 8px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(150, 150, 150, 1);
          font-size: 12px;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 100px;
          margin: 1px 0 0 3px;
        }
        .block_5 {
          background-color: rgba(150, 150, 150, 1);
          width: 15px;
          height: 14px;
          margin-left: 25px;
        }
        .text_44 {
          width: 8px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(150, 150, 150, 1);
          font-size: 12px;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 100px;
          margin: 1px 0 0 3px;
        }
        .image-text_12 {
          width: 42px;
          height: 12px;
          margin: 1px 0 0 26px;
          .box_20 {
            background-color: rgba(150, 150, 150, 1);
            width: 13px;
            height: 12px;
          }
          .text-group_12 {
            width: 25px;
            height: 12px;
            overflow-wrap: break-word;
            color: rgba(150, 150, 150, 1);
            font-size: 12px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 100px;
          }
        }
      }
    }
    .group_22 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 0px 0px 5px 5px;
      position: absolute;
      left: 193px;
      top: 661px;
      width: 170px;
      height: 143px;
      .group_23 {
        width: 101px;
        height: 17px;
        margin: 10px 0 0 11px;
        .text_45 {
          width: 46px;
          height: 15px;
          overflow-wrap: break-word;
          color: rgba(0, 0, 0, 1);
          font-size: 15px;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 100px;
        }
        .text-wrapper_20 {
          background-color: rgba(30, 30, 30, 1);
          border-radius: 50px;
          height: 16px;
          margin-top: 1px;
          width: 47px;
          .text_46 {
            width: 33px;
            height: 8px;
            overflow-wrap: break-word;
            color: rgba(234, 225, 196, 1);
            font-size: 10px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 100px;
            margin: 3px 0 0 7px;
          }
        }
      }
      .image-text_13 {
        width: 73px;
        height: 12px;
        margin: 28px 0 0 12px;
        .thumbnail_11 {
          width: 9px;
          height: 9px;
          margin-top: 2px;
        }
        .text-group_13 {
          width: 61px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 12px;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 100px;
        }
      }
      .group_24 {
        width: 146px;
        height: 27px;
        margin: 11px 0 0 13px;
        .text-wrapper_21 {
          border-radius: 4px;
          height: 27px;
          border: 1px solid rgba(11, 206, 148, 1);
          width: 67px;
          .text_47 {
            width: 51px;
            height: 15px;
            overflow-wrap: break-word;
            color: rgba(11, 206, 148, 1);
            font-size: 12px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 100px;
            margin: 7px 0 0 8px;
          }
        }
        .text-wrapper_22 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 5px;
          height: 27px;
          width: 67px;
          .text_48 {
            width: 49px;
            height: 12px;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 12px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 100px;
            margin: 7px 0 0 11px;
          }
        }
      }
      .group_25 {
        width: 146px;
        height: 14px;
        margin: 17px 0 7px 13px;
        .box_21 {
          background-color: rgba(150, 150, 150, 1);
          width: 16px;
          height: 14px;
        }
        .text_49 {
          width: 8px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(150, 150, 150, 1);
          font-size: 12px;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 100px;
          margin: 1px 0 0 3px;
        }
        .box_22 {
          background-color: rgba(150, 150, 150, 1);
          width: 15px;
          height: 14px;
          margin-left: 25px;
        }
        .text_50 {
          width: 8px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(150, 150, 150, 1);
          font-size: 12px;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 100px;
          margin: 1px 0 0 3px;
        }
        .image-text_14 {
          width: 42px;
          height: 12px;
          margin: 1px 0 0 26px;
          .group_26 {
            background-color: rgba(150, 150, 150, 1);
            width: 13px;
            height: 12px;
          }
          .text-group_14 {
            width: 25px;
            height: 12px;
            overflow-wrap: break-word;
            color: rgba(150, 150, 150, 1);
            font-size: 12px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 100px;
          }
        }
      }
    }
    .text-wrapper_23 {
      position: absolute;
      left: -395px;
      top: 884px;
      width: 168px;
      height: 168px;
      background: url(/static/lanhu_shouyejishikapian/FigmaDDSSlicePNGa911c964ed32937659ea639dfb0c05a2.png)
        100% no-repeat;
      background-size: 100% 100%;
      .text_51 {
        width: 24px;
        height: 12px;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 12px;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
        margin: 118px 0 0 430px;
      }
      .text_52 {
        width: 24px;
        height: 12px;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 12px;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
        margin: 118px -379px 0 69px;
      }
    }
    .text-wrapper_24 {
      background-color: rgba(16, 16, 16, 0.1);
      border-radius: 100px;
      height: 23px;
      width: 70px;
      position: absolute;
      left: 328px;
      top: 101px;
      .text_53 {
        width: 52px;
        height: 19px;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 13px;
        font-family: Source Han Sans CN-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 13px;
        margin: 2px 0 0 9px;
      }
    }
    .group_27 {
      background-color: rgba(255, 255, 255, 1);
      position: absolute;
      left: 0;
      top: 677px;
      width: 375px;
      height: 49px;
      .image-text_15 {
        width: 24px;
        height: 39px;
        margin-top: 6px;
        .label_2 {
          width: 22px;
          height: 22px;
          margin-left: 1px;
        }
        .text-group_15 {
          width: 24px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 12px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin-top: 5px;
        }
      }
      .image-text_16 {
        width: 24px;
        height: 38px;
        margin-top: 7px;
        .block_6 {
          height: 22px;
          background: url(/static/lanhu_shouyejishikapian/a1163f772a4e46fcade03d5393284bde_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 1px;
          width: 22px;
          .group_28 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 50%;
            width: 7px;
            height: 7px;
            margin: 3px 0 0 10px;
          }
        }
        .text-group_16 {
          width: 24px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 12px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin-top: 4px;
        }
      }
      .image-text_17 {
        width: 24px;
        height: 38px;
        margin-top: 7px;
        .label_3 {
          width: 22px;
          height: 22px;
          margin-left: 1px;
        }
        .text-group_17 {
          width: 24px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 12px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin-top: 4px;
        }
      }
      .image-text_18 {
        width: 24px;
        height: 38px;
        margin-top: 7px;
        .label_4 {
          width: 22px;
          height: 22px;
          margin-left: 1px;
        }
        .text-group_18 {
          width: 24px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 12px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin-top: 4px;
        }
      }
    }
    .text-wrapper_25 {
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: 5px;
      height: 34px;
      width: 94px;
      position: absolute;
      left: 69px;
      top: 486px;
      .text_54 {
        width: 56px;
        height: 14px;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 14px;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
        margin: 10px 0 0 19px;
      }
    }
  }
}
